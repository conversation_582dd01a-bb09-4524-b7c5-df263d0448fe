import { useState, useEffect } from "react";

/**
 * Hook, kuris seka elementų matomumą ekrane slenkant puslapį.
 * 
 * @param options - Hook'o konfigūracijos parametrai
 * @param options.selector - CSS selektorius elementams, kuriuos norime sekti
 * @param options.threshold - <PERSON>len<PERSON><PERSON> (0-1), nurodantis, kokia elemento dalis turi būti matoma
 * @param options.rootMargin - Papildoma erdvė aplink viewport, kur elementai laikomi matomi
 * @param options.once - Ar elementas turi būti pažymėtas kaip matomas tik vieną kartą
 * @returns Matomų elementų indeksų masyvas
 */
export function useScrollVisibility({
  selector,
  threshold = 0.85,
  rootMargin = "0px",
  once = true,
}: {
  selector: string;
  threshold?: number;
  rootMargin?: string;
  once?: boolean;
}) {
  const [visibleItems, setVisibleItems] = useState<number[]>([]);

  useEffect(() => {
    // Funkcija, kuri tikrina elementų matomumą
    const handleScroll = () => {
      const elements = document.querySelectorAll(selector);
      elements.forEach((element, elementIndex) => {
        // Jei elementas jau pažymėtas kaip matomas ir naudojame "once" režimą, praleidžiame
        if (once && visibleItems.includes(elementIndex)) {
          return;
        }

        const elementRect = element.getBoundingClientRect();
        const isVisible = elementRect.top < window.innerHeight * threshold;

        if (isVisible && !visibleItems.includes(elementIndex)) {
          setVisibleItems((previousItems) => [...previousItems, elementIndex]);
        } else if (!isVisible && !once && visibleItems.includes(elementIndex)) {
          setVisibleItems((previousItems) =>
            previousItems.filter((index) => index !== elementIndex)
          );
        }
      });
    };

    // Pridedame scroll event listener
    window.addEventListener("scroll", handleScroll);
    
    // Tikriname matomumą iš karto po komponento užkrovimo
    handleScroll();

    // Išvalome event listener, kai komponentas išmontuojamas
    return () => window.removeEventListener("scroll", handleScroll);
  }, [visibleItems, selector, threshold, rootMargin, once]);

  return visibleItems;
}
