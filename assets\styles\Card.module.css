/* Bendri k<PERSON>eli<PERSON> stiliai */
.card {
  @apply bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200;
  @apply shadow-sm hover:shadow-md transition-all duration-300;
  @apply p-6;
}

/* Tamsios temos kortelė */
.cardDark {
  @apply bg-gray-800/90 backdrop-blur-sm rounded-xl border border-gray-700;
  @apply shadow-sm hover:shadow-md transition-all duration-300;
  @apply p-6;
}

/* Kortelė su gradientu */
.cardGradient {
  @apply bg-gradient-to-br from-white/90 to-gray-50/90;
  @apply backdrop-blur-sm rounded-xl border border-gray-200;
  @apply shadow-sm hover:shadow-lg transition-all duration-300;
  @apply p-6;
}

/* Interaktyvi kortelė */
.cardInteractive {
  @apply cursor-pointer transform transition-all duration-300;
  @apply hover:translate-y-[-4px] hover:shadow-xl;
}

/* Kortelė su hover efektu */
.cardHover {
  @apply transition-all duration-300;
}

.cardHover:hover {
  @apply bg-white/95 border-blue-300 shadow-lg;
}

/* <PERSON><PERSON><PERSON><PERSON><PERSON> */
.cardHeader {
  @apply flex items-center gap-3 mb-4;
}

/* Kortelės pavadinimas */
.cardTitle {
  @apply text-xl font-semibold text-gray-900;
}

/* Kortelės aprašymas */
.cardDescription {
  @apply text-gray-600 leading-relaxed;
}

/* Kortelės ikona */
.cardIcon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center;
  @apply bg-blue-500/10 text-blue-500;
}

/* Kortelės veiksmai */
.cardActions {
  @apply flex gap-3 mt-6 pt-4 border-t border-gray-200;
}

/* Kompaktiška kortelė */
.cardCompact {
  @apply p-4;
}

.cardCompact .cardTitle {
  @apply text-lg;
}

.cardCompact .cardIcon {
  @apply w-8 h-8;
}

/* Plati kortelė */
.cardWide {
  @apply p-8;
}

.cardWide .cardTitle {
  @apply text-2xl;
}

.cardWide .cardIcon {
  @apply w-16 h-16;
}

/* Kortelė su šešėliu */
.cardShadow {
  @apply shadow-lg;
}

/* Kortelė be kraštų */
.cardBorderless {
  @apply border-0;
}

/* Kortelė su spalvotu kraštu */
.cardBorderColored {
  @apply border-l-4 border-l-blue-500;
}

/* Animuota kortelė */
.cardAnimated {
  @apply transition-all duration-500;
  @apply opacity-0 translate-y-8;
}

.cardAnimated.visible {
  @apply opacity-100 translate-y-0;
}

/* Kortelės grupė */
.cardGrid {
  @apply grid gap-6;
}

.cardGrid.cols1 {
  @apply grid-cols-1;
}

.cardGrid.cols2 {
  @apply grid-cols-1 md:grid-cols-2;
}

.cardGrid.cols3 {
  @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
}

.cardGrid.cols4 {
  @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-4;
}
