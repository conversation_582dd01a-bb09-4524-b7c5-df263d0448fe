import React, { ReactNode } from "react";
import { LucideIcon } from "lucide-react";

interface StatItem {
  icon: LucideIcon | ((props: { value: string }) => JSX.Element);
  value: string;
  label: string;
}

interface BenefitCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  items: string[];
  stats?: StatItem[];
  illustration: ReactNode;
  isVisible: boolean;
  index: number;
}

const BenefitCard = ({
  icon: Icon,
  title,
  description,
  items,
  stats,
  illustration,
  isVisible,
  index,
}: BenefitCardProps) => {
  return (
    <div
      className={`benefit-card flex flex-col ${
        index % 2 === 0 ? "lg:flex-row" : "lg:flex-row-reverse"
      } gap-8 lg:gap-16 items-center transition-all duration-700 transform ${
        isVisible
          ? "translate-y-0 opacity-100"
          : "translate-y-10 opacity-0"
      }`}
      style={{ transitionDelay: `${index * 100}ms` }}
    >
      {/* <PERSON><PERSON><PERSON><PERSON> dalis */}
      <div className="lg:w-1/2 space-y-4">
        <div className="flex items-center gap-3 mb-2">
          <div className="h-12 w-12 rounded-lg bg-nexos-purple/20 flex items-center justify-center">
            <Icon className="h-6 w-6 text-nexos-purple" />
          </div>
          <h3 className="text-2xl font-semibold text-[#262B30]">
            {title}
          </h3>
        </div>
        <p className="text-[#262B30] mb-6">{description}</p>
        <ul className="space-y-2">
          {items.map((item, itemIndex) => (
            <li key={itemIndex} className="flex items-start">
              <span className="text-nexos-purple mr-2">•</span>
              <span className="text-[#262B30]">{item}</span>
            </li>
          ))}
        </ul>

        {/* Statistikos ikonos */}
        {stats && (
          <div className="flex flex-wrap gap-4 mt-6">
            {stats.map((stat, statIndex) => (
              <div
                key={statIndex}
                className="flex flex-col items-center bg-nexos-dark-lighter rounded-lg p-3 w-24"
              >
                {typeof stat.icon === "function" ? (
                  <stat.icon value={stat.value} />
                ) : (
                  <div className="relative flex items-center justify-center">
                    <stat.icon className="h-6 w-6 text-nexos-purple" />
                  </div>
                )}
                <span className="text-[#262B30] font-bold mt-1">
                  {stat.value}
                </span>
                <span className="text-[#262B30] text-xs text-center">
                  {stat.label}
                </span>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Vizualinė dalis - modernios iliustracijos */}
      <div className="lg:w-1/2 h-64 md:h-80 nexos-card flex items-center justify-center">
        {illustration}
      </div>
    </div>
  );
};

export default BenefitCard;
