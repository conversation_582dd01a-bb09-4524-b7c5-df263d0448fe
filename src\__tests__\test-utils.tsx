import React, { ReactElement } from "react";
import { render, RenderOptions } from "@testing-library/react";
import { MemoryRouter } from "react-router-dom";
import { TooltipProvider } from "@/shared/ui/tooltip";
import AppQueryClientProvider from "@/shared/contexts/QueryClientContext";

interface CustomRenderOptions extends Omit<RenderOptions, "wrapper"> {
  route?: string;
  routes?: string[];
}

// Pagalbinė funkcija komponentų renderinimui su visais reikalingais provideriais
function customRender(ui: ReactElement, options?: CustomRenderOptions) {
  const { route = "/", routes = [route], ...renderOptions } = options || {};

  const Wrapper = ({ children }: { children: React.ReactNode }) => {
    return (
      <AppQueryClientProvider defaultOptions={{ queries: { retry: false } }}>
        <TooltipProvider>
          <MemoryRouter initialEntries={routes}>{children}</MemoryRouter>
        </TooltipProvider>
      </AppQueryClientProvider>
    );
  };

  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

// Re-eksportuojame viską iš testing-library
export * from "@testing-library/react";
export { customRender as render };
