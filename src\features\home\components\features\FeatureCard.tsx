import { useState } from "react";
import { LucideIcon, ArrowRight } from "lucide-react";

interface FeatureCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  isVisible: boolean;
  index: number;
  isPopular?: boolean;
}

const FeatureCard = ({
  icon: Icon,
  title,
  description,
  isVisible,
  index,
  isPopular = false
}: FeatureCardProps) => {
  const [isCardHovered, setIsCardHovered] = useState(false);

  return (
    <div
      className={`feature-card relative transform transition-all duration-500 ${
        isVisible ? "translate-y-0 opacity-100" : "translate-y-10 opacity-0"
      } group overflow-hidden rounded-3xl hover:translate-y-[-5px] ${
        index === 0 ? "md:-translate-y-6" : ""
      }`}
      style={{ transitionDelay: `${index * 100}ms` }}
      onMouseEnter={() => setIsCardHovered(true)}
      onMouseLeave={() => setIsCardHovered(false)}
    >
      {/* <PERSON><PERSON><PERSON><PERSON>s background su efektu */}
      <div
        className={`absolute inset-0 backdrop-blur-sm rounded-3xl transition-all duration-300
        border group-hover:border-[#921de0]/50 group-hover:bg-[#8744ab]/10
        group-hover:shadow-xl group-hover:shadow-[#921de0]/20 z-0 ${
          index === 0
            ? "bg-gradient-to-b from-[#921de0]/20 to-[#8744ab]/20 border-[#921de0]/30"
            : "bg-[#8744ab]/10 border-white/10"
        }`}
      >
        {/* Dekoratyvinis kampas - pataisytas, kad neišeitų už kortelės ribų */}
        <div className="absolute top-0 right-0 w-24 h-24 bg-[#921de0]/20 rounded-bl-full opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

        {/* Vidinis rėmelis */}
        <div
          className={`absolute inset-[8px] rounded-2xl border ${
            index === 0
              ? "border-[#921de0]/30 bg-gradient-to-b from-[#921de0]/10 to-[#8744ab]/10"
              : "border-[#8744ab]/20 bg-[#290C38]/20"
          }`}
        ></div>

        {/* Specialus žymėjimas pirmajai kortelei */}
        {isPopular && (
          <>
            {/* Juostelė kampe - pataisyta, kad būtų geriau matoma */}
            <div className="absolute -top-1 -right-1 z-10">
              <div className="bg-gradient-to-r from-[#921de0] to-[#8744ab] text-white text-xs font-bold py-1.5 px-5 rounded-br-xl rounded-tl-xl shadow-lg transform rotate-0 origin-top-right border border-white/10">
                POPULIARU
              </div>
            </div>

            {/* Subtilus švytėjimas aplink kortelę */}
            <div className="absolute -inset-0.5 bg-[#921de0]/20 rounded-3xl blur-sm opacity-40"></div>

            {/* Papildomas efektas - linija viršuje */}
            <div className="absolute top-0 left-[10%] right-[10%] h-0.5 bg-gradient-to-r from-transparent via-[#921de0]/50 to-transparent"></div>
          </>
        )}
      </div>

      {/* Kortelės turinys */}
      <div className="relative z-10 p-8 flex flex-col h-full">
        {/* Ikona kvadrato formoje su apvaliais kraštais */}
        <div className="flex justify-center w-full mb-8">
          <div className="relative">
            {/* Tamsus kvadratas su apvaliais kampais */}
            <div
              className="h-20 w-20 bg-[#290C38] rounded-2xl transform transition-all duration-300
              group-hover:bg-[#3A1249] shadow-lg border border-[#921de0]/30 flex items-center justify-center"
            >
              {/* Ikona */}
              <Icon
                className={`h-9 w-9 text-[#921de0] transform transition-all duration-300
                ${isCardHovered ? "scale-110" : ""}`}
              />
            </div>
          </div>
        </div>

        {/* Tekstas - centruotas */}
        <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-[#921de0] transition-colors duration-300 text-center">
          {title}
        </h3>
        <p className="text-gray-300 mb-5 flex-grow text-base leading-relaxed text-center">
          {description}
        </p>

        {/* Learn More mygtukas su efektu - centruotas */}
        <div className="mt-auto pt-2 flex justify-center">
          <a
            href="#"
            className="inline-flex items-center text-[#921de0] font-medium
            transition-all duration-300 relative group"
          >
            <span className="relative z-10 nexos-shimmer-text">
              Sužinoti Daugiau
            </span>
            <ArrowRight className="ml-2 h-5 w-5 transform transition-transform duration-300 group-hover:translate-x-2" />
          </a>
        </div>
      </div>
    </div>
  );
};

export default FeatureCard;
