import React from "react";
import { Navbar, Footer } from "@/shared/layout";
import { <PERSON>, Hero2, TechnologyPartners, Features, Testimonials, Benefits, ReadyToStart } from "@/features/home";
import { Workflow } from "@/features/how-it-works";
import { UseCases } from "@/features/use-cases";
import { FAQ } from "@/features/faq";
import { Button } from "@/shared/ui/button";
import { Link } from "react-router-dom";

const Index = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="flex-grow">
        <Hero />
        <Hero2 />
        <TechnologyPartners />
        <Features />
        <Workflow />
        <Benefits />
        {/* <UseCases /> */}
        <Testimonials />
        <FAQ />
        {/* CTA Section */}
        <ReadyToStart /> {/* Naudojame naują komponentą */}
      </main>

      <Footer />
    </div>
  );
};

export default Index;
