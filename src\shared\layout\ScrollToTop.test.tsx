import { describe, it, expect, vi, beforeEach } from "vitest";
import { render } from "../test/test-utils";
import { Route, Routes } from "react-router-dom";
import ScrollToTop from "./ScrollToTop";

describe("ScrollToTop", () => {
  beforeEach(() => {
    // Mockuojame window.scrollTo metodą
    window.scrollTo = vi.fn();
  });

  it("scrolls to top when route changes", () => {
    // Pirmiausia renderuojame su pradiniu route
    render(
      <>
        <ScrollToTop />
        <Routes>
          <Route path="/" element={<div>Home Page</div>} />
          <Route path="/about" element={<div>About Page</div>} />
        </Routes>
      </>,
      { route: "/" }
    );

    // Tikriname, ar scrollTo buvo iškviesta pradiniam route
    expect(window.scrollTo).toHaveBeenCalledWith(0, 0);

    // Nustatome scrollTo iškviestų kartų skaitliuką iš naujo
    vi.clearAllMocks();

    // Dabar renderuojame su kitu route
    render(
      <>
        <ScrollToTop />
        <Routes>
          <Route path="/" element={<div>Home Page</div>} />
          <Route path="/about" element={<div>About Page</div>} />
        </Routes>
      </>,
      { route: "/about" }
    );

    // Tikriname, ar scrollTo buvo iškviesta po route pakeitimo
    expect(window.scrollTo).toHaveBeenCalledWith(0, 0);
  });

  it("renders nothing to the DOM", () => {
    const { container } = render(<ScrollToTop />);

    // Tikriname, ar komponentas neturi jokių DOM elementų
    expect(container.firstChild).toBeNull();
  });
});
