import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen } from "../test/test-utils";
import { Route, Routes } from "react-router-dom";
import { act } from "react";
import PageLoader from "./PageLoader";

// Mockuojame LoadingBar komponentą
vi.mock("./LoadingBar", () => ({
  default: ({ isLoading }: { isLoading: boolean }) => (
    <div data-testid="loading-bar" data-loading={isLoading.toString()}>
      Loading Bar
    </div>
  ),
}));

describe("PageLoader", () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("shows loading bar when route changes", () => {
    render(
      <>
        <PageLoader />
        <Routes>
          <Route path="/" element={<div>Home Page</div>} />
          <Route path="/about" element={<div>About Page</div>} />
        </Routes>
      </>,
      { route: "/" }
    );

    // Tikriname, ar LoadingBar rodomas su isLoading=true
    const loadingBar = screen.getByTestId("loading-bar");
    expect(loadingBar).toBeInTheDocument();
    expect(loadingBar.getAttribute("data-loading")).toBe("true");
  });

  it("hides loading bar after timeout", () => {
    render(<PageLoader />, { route: "/" });

    // Tikriname, ar LoadingBar rodomas su isLoading=true
    let loadingBar = screen.getByTestId("loading-bar");
    expect(loadingBar.getAttribute("data-loading")).toBe("true");

    // Palaukiame, kol baigsis įkrovimo laikas (600ms) ir naudojame act
    act(() => {
      vi.advanceTimersByTime(600);
    });

    // Tikriname, ar LoadingBar dabar turi isLoading=false
    loadingBar = screen.getByTestId("loading-bar");
    expect(loadingBar.getAttribute("data-loading")).toBe("false");
  });
});
