import { Routes, Route } from "react-router-dom";
import Index from "../pages/Index";
import NotFound from "../pages/NotFound";
import Services from "../pages/Services";
import HowItWorks from "../pages/HowItWorks";
import UseCases from "../pages/UseCases";
import FAQ from "../pages/FAQ";
import About from "../pages/About";
import ContactSales from "../pages/ContactSales";

const AppRouter = () => (
  <Routes>
    <Route path="/" element={<Index />} />
    <Route path="/paslaugos" element={<Services />} />
    <Route path="/kaip-tai-veikia" element={<HowItWorks />} />
    <Route path="/naudojimo-atvejai" element={<UseCases />} />
    <Route path="/duk" element={<FAQ />} />
    <Route path="/apie-mus" element={<About />} />
    <Route path="/contact-sales" element={<ContactSales />} />
    {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
    <Route path="*" element={<NotFound />} />
  </Routes>
);

export default AppRouter;
