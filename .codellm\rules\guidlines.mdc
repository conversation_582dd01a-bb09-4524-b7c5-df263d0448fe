---
description: Coding guidlines
globs: 
alwaysApply: false
---

pateikti info lietuviu kalba.



# Enhanced Programming Guidelines for AI Coding Agent

## 1. Code Quality and Readability

- Meaningful Naming**: Use descriptive, intention-revealing names for variables, functions, classes and files that explain their purpose, usage, and behavior.
* **Simplicity First**: Write code that prioritizes clarity over complexity. Prefer simple, straightforward solutions that are easy to understand and maintain.
* **Consistent Formatting**: Follow established style guides and formatting conventions for the specific programming language. Maintain uniform indentation, spacing, and code structure.
* **Comment Purposefully**: Write comments to explain "why" rather than "what" or "how". Good code is largely self-documenting through proper naming and structure.
* **Documentation**: Include clear documentation for APIs, modules, and complex algorithms to facilitate understanding and future maintenance.
* **Maximum Line Length**: Limit lines to a reasonable length (80-120 characters) to improve readability and prevent horizontal scrolling.
* **Whitespace Usage**: Use whitespace strategically to separate logical blocks and improve code readability.

## 2. Code Structure and Organization

* **Single Responsibility Principle**: Each component (class, function, module) should have only one reason to change, focusing on a single concern or functionality.
* **Modularity**: Organize code into logically coherent, self-contained modules that can be understood, tested, and maintained independently.
* **File Size Limits**: Keep files under 300 lines of code; split larger files into smaller, focused components with meaningful names.
* **Cohesion and Coupling**: Aim for high cohesion (related functionality grouped together) and low coupling (minimal dependencies between components).
* **Clean Abstractions**: Create clear abstractions that hide implementation details and expose only what's necessary. Avoid leaky abstractions.
* **Hierarchical Structure**: Organize code in a logical hierarchy that reflects the domain and makes navigation intuitive.
* **Separation of Concerns**: Clearly separate different aspects of the application (UI, business logic, data access, etc.).

## 3. SOLID Principles Implementation

* **Single Responsibility**: Each class should have only one reason to change, handling only one functionality or concern.
* **Open/Closed**: Software entities should be open for extension but closed for modification. Use inheritance and interfaces to allow behavior extension without changing existing code.
* **Liskov Substitution**: Subtypes must be substitutable for their base types without altering program correctness. Ensure inheritance hierarchies model "is-a" relationships properly.
* **Interface Segregation**: Clients should not be forced to depend on interfaces they don't use. Create focused, specific interfaces rather than large, general-purpose ones.
* **Dependency Inversion**: High-level modules should not depend on low-level modules. Both should depend on abstractions. Depend on abstractions, not concretions.

## 4. Function and Method Design

* **Function Size**: Keep functions short and focused. Each function should do one thing and do it well.
* **Parameter Count**: Limit the number of parameters (ideally 3 or fewer). Use objects for related parameters.
* **Pure Functions**: Prefer pure functions (same input always gives same output, no side effects) when possible.
* **Command-Query Separation**: Separate functions that change state (commands) from functions that return values (queries).
* **Early Returns**: Use early returns to reduce nesting and complexity, making control flow clearer.
* **Default Parameters**: Provide sensible defaults for parameters when appropriate.
* **Consistent Return Types**: Ensure consistent return types and patterns throughout the codebase.

## 5. Error Handling and Safety

* **Explicit Error Handling**: Handle errors explicitly and gracefully. Don't suppress exceptions without proper handling.
* **Fail Fast**: Detect errors as early as possible to prevent cascading failures and simplify debugging.
* **Defensive Programming**: Validate inputs and guard against edge cases, but avoid excessive defensive code that obscures the main logic.
* **Meaningful Error Messages**: Provide clear, actionable error messages that help diagnose and resolve issues.
* **Exception Hierarchies**: Create appropriate exception hierarchies that allow for specific handling of different error types.
* **Avoid Null**: Minimize use of null values; consider using Option/Maybe patterns or null object pattern instead.
* **Recovery Mechanisms**: Implement appropriate recovery mechanisms for critical systems to ensure resilience.

## 6. Performance and Optimization

* **Premature Optimization**: Avoid premature optimization. First make it correct, then make it fast if and where necessary.
* **Measure First**: Use profiling tools to identify actual bottlenecks before optimizing code.
* **Algorithmic Efficiency**: Choose appropriate data structures and algorithms for the task at hand.
* **Resource Management**: Properly manage resources like file handles, network connections, and memory. Use language-specific idioms like "using" blocks or try-with-resources.
* **Caching Strategy**: Implement appropriate caching strategies for expensive operations.
* **Asynchronous Operations**: Use asynchronous operations for I/O-bound tasks to improve responsiveness.
* **Load Testing**: Design with scalability in mind and verify through load testing.

## 7. Testing and Testability

* **Test-Driven Development**: Consider writing tests first to drive the design and ensure testability.
* **Comprehensive Test Coverage**: Write unit, integration, and system tests to verify different aspects of the code.
* **Test Independence**: Ensure tests are independent and can run in any order.
* **Mocking and Stubbing**: Use mock objects and stubs to isolate components for testing.
* **Test Readability**: Make tests readable and maintainable with clear setup, execution, and assertion phases.
* **Testing Edge Cases**: Include tests for edge cases, error conditions, and boundary values.
* **Continuous Integration**: Integrate testing into the development workflow through CI/CD pipelines.

## 8. Security Best Practices

* **Input Validation**: Validate all input from external sources to prevent injection attacks.
* **Authentication and Authorization**: Implement proper access controls and permission checks.
* **Secure Communication**: Use secure protocols and encryption for sensitive data transmission.
* **Principle of Least Privilege**: Grant minimum necessary access rights to components and users.
* **Security Updates**: Keep dependencies updated to protect against known vulnerabilities.
* **Sensitive Data Handling**: Handle sensitive data with appropriate security measures (encryption, secure storage, minimal retention).
* **Security Testing**: Conduct regular security testing and code reviews focusing on security aspects.

## 9. Version Control and Collaboration

* **Atomic Commits**: Make small, focused commits that address a single concern.
* **Descriptive Commit Messages**: Write clear commit messages that explain what changed and why.
* **Feature Branches**: Use feature branches for new development to isolate changes.
* **Regular Integration**: Integrate changes regularly to detect conflicts early.
* **Code Reviews**: Review all code changes for quality, security, and consistency before merging.
* **Documentation Updates**: Update documentation alongside code changes to keep it current.
* **Continuous Integration**: Use CI/CD pipelines to automate building, testing, and deployment.

## 10. Dependency Management

* **Minimal Dependencies**: Keep external dependencies to a minimum to reduce complexity and vulnerability surface.
* **Dependency Evaluation**: Thoroughly evaluate new dependencies for security, maintenance status, and license compatibility.
* **Explicit Versioning**: Specify exact versions of dependencies to ensure reproducible builds.
* **Dependency Isolation**: Isolate dependencies to minimize their impact on the rest of the system.
* **Dependency Updates**: Regularly update dependencies to get bug fixes and security patches.
* **Vendor Management**: Consider vendoring critical dependencies or having fallback strategies.
* **Dependency Injection**: Use dependency injection to improve testability and flexibility.

## 11. Documentation and Knowledge Sharing

* **Self-Documenting Code**: Write code that expresses intent clearly through good naming and structure.
* **API Documentation**: Document public APIs thoroughly, including parameters, return values, exceptions, and usage examples.
* **Architecture Documentation**: Document high-level architecture decisions and system structure.
* **Code Comments**: Use comments to explain non-obvious aspects, known limitations, and the reasoning behind complex solutions.
* **README Files**: Maintain updated README files with setup instructions, usage guidelines, and contribution processes.
* **Knowledge Transfer**: Foster knowledge sharing through pair programming, code reviews, and documentation.
* **Living Documentation**: Keep documentation up-to-date as the code evolves.

## 12. Design Patterns and Best Practices

- Appropriate Patterns**: Use established design patterns to solve common problems, but avoid over-engineering.
- Consistent Patterns**: Apply design patterns consistently throughout the codebase.
- Pattern Understanding**: Ensure the team understands the patterns being used and their implications.
- Language Idioms**: Follow language-specific idioms and best practices.
- Domain-Driven Design**: Align code structure with the business domain for better understanding and communication.
- Microservices Considerations**: If using microservices, design with appropriate boundaries, communication patterns, and fault tolerance.
- Refactoring Patterns**: Apply established refactoring patterns to improve code quality incrementally.

## 13. Code Maintenance and Refactoring

- Boy Scout Rule**: Leave code better than you found it. Make small improvements when working with existing code.
- Technical Debt Management**: Identify, document, and plan to address technical debt.
- Regular Refactoring**: Schedule regular refactoring sessions to improve code quality.
- Incremental Changes**: Make small, incremental changes rather than large rewrites when refactoring.
- Refactoring with Tests**: Ensure robust tests are in place before refactoring to catch regressions.
- Code Smells Awareness**: Be alert to code smells that indicate potential design problems.
- Legacy Code Strategies**: Develop strategies for working with and gradually improving legacy code.

## 14. Environment and Configuration Management

* **Configuration Separation**: Keep configuration separate from code.
* **Environment-Specific Settings**: Manage environment-specific settings through appropriate configuration mechanisms.
* **Secret Management**: Handle secrets and credentials securely, never hardcoding them in the source code.
* **Configuration Validation**: Validate configuration at startup to fail fast if critical settings are missing.
* **Default Configurations**: Provide sensible defaults where possible to minimize required configuration.
* **Documentation**: Document all configuration options, their purpose, and acceptable values.
* **Feature Flags**: Use feature flags for conditional functionality and easier deployment.

## 15. Continuous Integration and Deployment

* **Automated Builds**: Set up automated builds to ensure code compiles and passes tests.
* **Continuous Testing**: Run automated tests on every build to catch issues early.
* **Deployment Automation**: Automate deployment processes to reduce human error.
* **Rolling Deployments**: Consider rolling or blue-green deployments to minimize downtime.
* **Monitoring Integration**: Integrate monitoring and alerting into the deployment process.
* **Rollback Capability**: Ensure deployments can be rolled back quickly if issues are detected.
* **Deployment Documentation**: Document the deployment process and recovery procedures.