
import { <PERSON>vbar, <PERSON>er } from "@/shared/layout";
import { But<PERSON> } from "@/shared/ui/button";
import { Link } from "react-router-dom";
import { FAQBase, FAQCategoryData } from "@/features/faq";
import { HeroBase } from "@/features/home";

const FAQ = () => {
  // FAQ kategorijų duomenys
  const faqCategories: FAQCategoryData[] = [
    {
      title: "Bendrieji klausimai",
      items: [
        {
          question: "Kas yra Roqus Analytics?",
          answer: `Roqus Analytics yra duomenų analitikos įmonė, kuri padeda
                  verslams priimti geresnius sprendimus, paremtus
                  duomenimis. Mes specializuojamės Power BI ataskaitų
                  kūrime, duomenų bazių optimizavime ir ETL procesų
                  automatizavime. Mūsų tikslas – paversti sudėtingus
                  duomenis į aiškias, lengvai suprantamas įžvalgas, kurios
                  padeda verslams augti ir tobul<PERSON>ti.`,
          value: "item-1"
        },
        {
          question: "Kaip Roqus Analytics gali padėti mano verslui?",
          answer: `Mūsų paslaugos padeda verslams:
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li>Vizualizuoti verslo duomenis interaktyviose Power BI ataskaitose</li>
                    <li>Automatizuoti duomenų rinkimą ir apdorojimą (ETL procesus)</li>
                    <li>Optimizuoti duomenų bazes geresniam veikimui</li>
                    <li>Priimti duomenimis pagrįstus sprendimus</li>
                    <li>Sutaupyti laiką ir išteklius, kurie anksčiau buvo skiriami rankiniam ataskaitų rengimui</li>
                  </ul>
                  Mūsų sprendimai pritaikomi įvairaus dydžio ir sektorių įmonėms – nuo mažų verslų iki didelių korporacijų.`,
          value: "item-2"
        }
      ]
    },
    {
      title: "Power BI klausimai",
      items: [
        {
          question: "Kas yra Power BI ir kodėl turėčiau jį naudoti?",
          answer: `Power BI yra Microsoft verslo analitikos įrankis,
                  leidžiantis vizualizuoti duomenis interaktyviose
                  ataskaitose ir dashboarduose. Jis padeda paversti įvairius
                  duomenų šaltinius į aiškias vizualizacijas ir įžvalgas.
                  <br /><br />
                  Jums verta naudoti Power BI, nes:
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li>Jis sujungia duomenis iš skirtingų šaltinių į vieną vietą</li>
                    <li>Leidžia kurti interaktyvias ataskaitas, kurias galima filtruoti realiu laiku</li>
                    <li>Suteikia galimybę sekti svarbiausius verslo rodiklius (KPI)</li>
                    <li>Padeda atskleisti tendencijas ir modelius, kurie kitaip liktų nepastebėti</li>
                    <li>Leidžia dalintis įžvalgomis su komanda ir suinteresuotomis šalimis</li>
                  </ul>`,
          value: "item-3"
        },
        {
          question: "Ar man reikia turėti Power BI licenciją, kad naudočiausi jūsų paslaugomis?",
          answer: `Tai priklauso nuo jūsų pasirinkto bendradarbiavimo modelio:
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li><span className="font-semibold">Power BI ataskaitų nuoma:</span> Nereikia jokių licencijų – mes pasirūpiname visais techniniais aspektais.</li>
                    <li><span className="font-semibold">Power BI ataskaitų kūrimas jūsų aplinkai:</span> Jums reikės Power BI Pro licencijų (apie 10 EUR/mėn. vienam vartotojui) arba Premium licencijos (kaina priklauso nuo įmonės dydžio).</li>
                  </ul>
                  Mes galime patarti, koks licencijavimo modelis būtų optimaliausias jūsų verslui, atsižvelgiant į vartotojų skaičių ir poreikius.`,
          value: "item-4"
        },
        {
          question: "Kuo skiriasi Power BI ataskaitų kūrimas nuo ataskaitų nuomos?",
          answer: `<p className="mb-2"><span className="font-semibold">Power BI ataskaitų kūrimas:</span></p>
                  <ul className="list-disc pl-5 mb-4 space-y-1">
                    <li>Vienkartinis projektas su fiksuota kaina</li>
                    <li>Ataskaitos publikuojamos jūsų Power BI aplinkoje</li>
                    <li>Jūs tampate ataskaitų savininku</li>
                    <li>Reikalingos Power BI licencijos</li>
                    <li>Papildomi pakeitimai ar priežiūra – už papildomą mokestį</li>
                  </ul>
                  <p className="mb-2"><span className="font-semibold">Power BI ataskaitų nuoma:</span></p>
                  <ul className="list-disc pl-5 space-y-1">
                    <li>Mėnesinis mokestis be didelės pradinės investicijos</li>
                    <li>Ataskaitos talpinamos mūsų infrastruktūroje</li>
                    <li>Mes prižiūrime ir atnaujiname ataskaitas</li>
                    <li>Nereikia jokių licencijų – viskas įskaičiuota į nuomos kainą</li>
                    <li>Reguliarūs atnaujinimai ir pakeitimai įtraukti į kainą</li>
                  </ul>`,
          value: "item-5"
        }
      ]
    },
    {
      title: "Techniniai klausimai",
      items: [
        {
          question: "Kokius duomenų šaltinius galite sujungti į Power BI ataskaitas?",
          answer: `Power BI gali jungtis prie daugybės duomenų šaltinių, įskaitant:
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li>Excel failai ir CSV duomenys</li>
                    <li>SQL duomenų bazės (Microsoft SQL Server, MySQL, PostgreSQL ir kt.)</li>
                    <li>Verslo sistemos (CRM, ERP, buhalterinės programos)</li>
                    <li>Debesų paslaugos (Salesforce, Dynamics 365, SharePoint)</li>
                    <li>Web servisai ir API</li>
                    <li>Socialinių tinklų duomenys</li>
                    <li>Ir daugelis kitų</li>
                  </ul>
                  Jei turite specifinį duomenų šaltinį, kurio nėra šiame sąraše, susisiekite su mumis – greičiausiai galėsime rasti sprendimą.`,
          value: "item-6"
        },
        {
          question: "Kas yra ETL procesai ir kodėl jie svarbūs?",
          answer: `ETL reiškia Extract (ištraukti), Transform (transformuoti) ir Load (užkrauti) – tai procesas, kurio metu:
                  <ol className="list-decimal pl-5 mt-2 space-y-1">
                    <li>Duomenys ištraukiami iš įvairių šaltinių</li>
                    <li>Transformuojami (valomi, formatuojami, agreguojami)</li>
                    <li>Užkraunami į tikslinę sistemą (duomenų saugyklą, analitinę platformą)</li>
                  </ol>
                  <br />
                  ETL procesai yra svarbūs, nes:
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li>Užtikrina duomenų kokybę ir nuoseklumą</li>
                    <li>Sujungia duomenis iš skirtingų sistemų</li>
                    <li>Paruošia duomenis analizei</li>
                    <li>Automatizuoja rutininius duomenų apdorojimo darbus</li>
                    <li>Sumažina klaidų tikimybę, lyginant su rankiniu duomenų apdorojimu</li>
                  </ul>`,
          value: "item-7"
        },
        {
          question: "Ar mano duomenys bus saugūs?",
          answer: `Taip, duomenų saugumas yra mūsų prioritetas:
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li>Dirbame pagal griežtus duomenų apsaugos standartus ir BDAR reikalavimus</li>
                    <li>Naudojame šifravimą duomenų perdavimui ir saugojimui</li>
                    <li>Taikome prieigos kontrolės mechanizmus</li>
                    <li>Pasirašome konfidencialumo sutartis (NDA)</li>
                    <li>Jei naudojate mūsų ataskaitų nuomos paslaugą, duomenys saugomi saugioje Microsoft Azure infrastruktūroje ES teritorijoje</li>
                    <li>Galime dirbti ir jūsų infrastruktūroje, jei to reikalauja jūsų saugumo politika</li>
                  </ul>`,
          value: "item-8"
        }
      ]
    },
    {
      title: "Bendradarbiavimo klausimai",
      items: [
        {
          question: "Kiek kainuoja jūsų paslaugos?",
          answer: `Mūsų kainodara priklauso nuo projekto apimties ir sudėtingumo:
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li><span className="font-semibold">Power BI ataskaitų kūrimas:</span> Nuo 500 EUR už paprastą ataskaitą iki 3000+ EUR už sudėtingas, daugialypes ataskaitas su sudėtingais duomenų modeliais.</li>
                    <li><span className="font-semibold">Power BI ataskaitų nuoma:</span> Nuo 100 EUR/mėn., priklausomai nuo ataskaitų skaičiaus ir sudėtingumo.</li>
                    <li><span className="font-semibold">ETL procesų kūrimas:</span> Nuo 800 EUR, priklausomai nuo duomenų šaltinių skaičiaus ir transformacijų sudėtingumo.</li>
                    <li><span className="font-semibold">SQL duomenų bazių optimizavimas:</span> Nuo 500 EUR, priklausomai nuo duomenų bazės dydžio ir sudėtingumo.</li>
                  </ul>
                  <br />
                  Kiekvienam klientui parengiame individualų pasiūlymą, atsižvelgdami į specifinius poreikius. Susisiekite su mumis, ir mes paruošime detalų kainų pasiūlymą.`,
          value: "item-9"
        },
        {
          question: "Kiek laiko užtrunka projekto įgyvendinimas?",
          answer: `Projekto trukmė priklauso nuo jo apimties ir sudėtingumo:
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li><span className="font-semibold">Paprastos Power BI ataskaitos:</span> 1-2 savaitės</li>
                    <li><span className="font-semibold">Sudėtingos, daugialypės ataskaitos:</span> 3-6 savaitės</li>
                    <li><span className="font-semibold">ETL procesų kūrimas:</span> 2-8 savaitės</li>
                    <li><span className="font-semibold">SQL duomenų bazių optimizavimas:</span> 1-4 savaitės</li>
                  </ul>
                  <br />
                  Projekto pradžioje sutariame dėl aiškaus terminų plano ir reguliariai informuojame apie projekto eigą.`,
          value: "item-10"
        },
        {
          question: "Ar teikiate mokymus ir palaikymą?",
          answer: `Taip, mes teikiame:
                  <ul className="list-disc pl-5 mt-2 space-y-1">
                    <li><span className="font-semibold">Mokymus:</span> Kiekvieno projekto pabaigoje apmokome jūsų komandą naudotis sukurtomis ataskaitomis. Taip pat siūlome išplėstinius Power BI mokymus už papildomą mokestį.</li>
                    <li><span className="font-semibold">Dokumentaciją:</span> Pateikiame išsamią dokumentaciją apie sukurtus sprendimus.</li>
                    <li><span className="font-semibold">Palaikymą:</span> Siūlome įvairius palaikymo planus, nuo bazinio (reagavimas per 48 val.) iki premium (reagavimas per 4 val., įskaitant savaitgalius).</li>
                    <li><span className="font-semibold">Atnaujinimus:</span> Jei naudojate mūsų ataskaitų nuomos paslaugą, reguliarūs atnaujinimai ir pakeitimai yra įtraukti į mėnesinį mokestį.</li>
                  </ul>`,
          value: "item-11"
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="flex-grow">
        {/* Hero Section */}
        <HeroBase
          title={
            <>
              Dažnai Užduodami{" "}
              <span className="nexos-gradient-text">Klausimai</span>
            </>
          }
          subtitle="Atsakymai į dažniausiai užduodamus klausimus apie Roqus Analytics platformą ir paslaugas."
          backgroundClassName="pt-24 pb-16 md:pb-20"
          backgroundStyle={{
            background: "linear-gradient(350deg,rgba(142,49,204,1) 0%,rgba(119,134,252,1) 0%,rgba(255,255,255,1) 100%,rgba(162,60,230,1) 100%)"
          }}
        >
          <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
            <div className="absolute top-[30%] right-[20%] w-[500px] h-[500px] bg-[#7786fc]/20 rounded-full blur-[150px] animate-pulse-glow"></div>
            <div className="absolute bottom-[20%] left-[30%] w-[300px] h-[300px] bg-[#7786fc]/30 rounded-full blur-[100px] animate-pulse-glow"></div>
          </div>
        </HeroBase>

        {/* FAQ Section */}
        <section className="py-16 bg-nexos-dark-light relative">
          <div className="nexos-container relative z-10">
            <div className="max-w-3xl mx-auto">
              <FAQBase
                categories={faqCategories}
                showCategories={true}
                accordionType="single"
              />
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 md:py-28 bg-nexos-dark-light relative">
          <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
            <div className="absolute top-[30%] right-[20%] w-[500px] h-[500px] bg-nexos-purple/10 rounded-full blur-[150px] animate-pulse-glow"></div>
            <div className="absolute bottom-[20%] left-[30%] w-[300px] h-[300px] bg-nexos-purple/15 rounded-full blur-[100px] animate-pulse-glow"></div>
          </div>

          <div className="nexos-container relative z-10">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="nexos-heading mb-6">
                Turite daugiau{" "}
                <span className="nexos-gradient-text">klausimų</span>?
              </h2>
              <p className="nexos-subheading mx-auto mb-8">
                Jei neradote atsakymo į savo klausimą, susisiekite su mumis ir
                mūsų komanda mielai jums padės.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/contact-sales">
                  <Button className="nexos-button-primary nexos-contact-button w-full md:w-auto">
                    Susisiekti
                  </Button>
                </Link>
                <Link to="/paslaugos">
                  <Button
                    variant="outline"
                    className="nexos-button-secondary w-full md:w-auto"
                  >
                    <span className="nexos-shimmer-text">Sužinoti daugiau</span>
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default FAQ;
