import React, { useRef, useEffect, useState, Suspense } from "react";
import { Canvas } from "@react-three/fiber";
import {
  OrbitControls,
  PerspectiveCamera,
  Environment,
  ContactShadows,
  Loader,
} from "@react-three/drei";
import { AnimatedChart } from "./AnimatedChart";
import ThreeEffects from "./ThreeEffects";
import { DEFAULT_ORBIT_CONTROLS_CONFIG } from "./types";

interface FaviconGeneratorProps {
  onGenerate: (dataUrl: string) => void;
  size?: number;
}

const FaviconGenerator: React.FC<FaviconGeneratorProps> = ({
  onGenerate,
  size = 512,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isRendered, setIsRendered] = useState(false);

  useEffect(() => {
    // Darome ekrano nuotrauką po trumpo laiko, kad komponentas spėtų užsikrauti
    const timer = setTimeout(() => {
      if (containerRef.current) {
        const canvas = containerRef.current.querySelector("canvas");
        if (canvas) {
          const dataUrl = canvas.toDataURL("image/png");
          onGenerate(dataUrl);
          setIsRendered(true);
        }
      }
    }, 2000); // Ilgesnis laikas, kad spėtų užsikrauti visi efektai

    return () => clearTimeout(timer);
  }, [onGenerate]);

  return (
    <div
      ref={containerRef}
      style={{
        width: size,
        height: size,
        position: "absolute",
        left: "-9999px",
        top: "-9999px",
        opacity: isRendered ? 0 : 1,
        background: "radial-gradient(circle, #0a0a20 0%, #050510 100%)",
      }}
    >
      <Canvas shadows dpr={[1, 2]}>
        <Suspense fallback={null}>
          <PerspectiveCamera makeDefault position={[0, 1.5, 3]} fov={40} />

          {/* Apšvietimas */}
          <ambientLight intensity={0.2} />
          <spotLight
            position={[5, 5, 5]}
            angle={0.15}
            penumbra={1}
            intensity={0.8}
            castShadow
            color="#ffffff"
          />
          <pointLight position={[-5, 5, -5]} intensity={0.5} color="#5CEFFF" />
          <pointLight position={[5, -5, 5]} intensity={0.5} color="#B026FF" />

          {/* Pagrindinis 3D grafikas */}
          <AnimatedChart />

          {/* Šešėliai */}
          <ContactShadows
            position={[0, -0.05, 0]}
            opacity={0.5}
            scale={10}
            blur={1.5}
            far={10}
            color="#000000"
          />

          {/* Aplinka */}
          <Environment preset="night" />

          {/* Post-processing efektai */}
          <ThreeEffects />

          {/* Kameros kontrolės */}
          <OrbitControls {...DEFAULT_ORBIT_CONTROLS_CONFIG} autoRotate={false} />
        </Suspense>
      </Canvas>

      {/* Loader kol kraunasi 3D elementai */}
      <Loader
        containerStyles={{
          background: "radial-gradient(circle, #0a0a20 0%, #050510 100%)",
        }}
        innerStyles={{ background: "#5CEFFF" }}
        barStyles={{ background: "#B026FF" }}
      />
    </div>
  );
};

export default FaviconGenerator;
