import { describe, it, expect, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import App from "./App";

// Mockuojame visus komponentus, kad testas būtų paprastesnis
vi.mock("@/shared/ui/toaster", () => ({
  Toaster: () => <div data-testid="toaster">Toaster</div>,
}));

vi.mock("@/shared/ui/sonner", () => ({
  Toaster: () => <div data-testid="sonner">Sonner</div>,
}));

vi.mock("@/shared/ui/tooltip", () => ({
  TooltipProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="tooltip-provider">{children}</div>
  ),
}));

vi.mock("./components/ScrollToTop", () => ({
  default: () => <div data-testid="scroll-to-top">ScrollToTop</div>,
}));

vi.mock("./components/PageLoader", () => ({
  default: () => <div data-testid="page-loader">PageLoader</div>,
}));

vi.mock("./pages/Index", () => ({
  default: () => <div data-testid="index-page">Index Page</div>,
}));

vi.mock("./pages/NotFound", () => ({
  default: () => <div data-testid="not-found-page">Not Found Page</div>,
}));

vi.mock("./pages/Services", () => ({
  default: () => <div data-testid="services-page">Services Page</div>,
}));

vi.mock("./pages/HowItWorks", () => ({
  default: () => <div data-testid="how-it-works-page">How It Works Page</div>,
}));

vi.mock("./pages/UseCases", () => ({
  default: () => <div data-testid="use-cases-page">Use Cases Page</div>,
}));

vi.mock("./pages/FAQ", () => ({
  default: () => <div data-testid="faq-page">FAQ Page</div>,
}));

vi.mock("./pages/About", () => ({
  default: () => <div data-testid="about-page">About Page</div>,
}));

vi.mock("./pages/ContactSales", () => ({
  default: () => <div data-testid="contact-sales-page">Contact Sales Page</div>,
}));

vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    BrowserRouter: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="browser-router">{children}</div>
    ),
    Routes: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="routes">{children}</div>
    ),
    Route: ({ path, element }: { path: string; element: React.ReactNode }) => (
      <div data-testid={`route-${path.replace("/", "")}`}>{element}</div>
    ),
  };
});

describe("App", () => {
  it("renders all required components", () => {
    render(<App />);

    // Tikriname, ar visi pagrindiniai komponentai yra sugeneruoti
    expect(screen.getByTestId("toaster")).toBeInTheDocument();
    expect(screen.getByTestId("sonner")).toBeInTheDocument();
    expect(screen.getByTestId("tooltip-provider")).toBeInTheDocument();
    expect(screen.getByTestId("browser-router")).toBeInTheDocument();
    expect(screen.getByTestId("scroll-to-top")).toBeInTheDocument();
    expect(screen.getByTestId("page-loader")).toBeInTheDocument();
    expect(screen.getByTestId("routes")).toBeInTheDocument();

    // Tikriname, ar visi maršrutai yra sugeneruoti
    expect(screen.getByTestId("route-")).toBeInTheDocument(); // Pagrindinis puslapis
    expect(screen.getByTestId("route-paslaugos")).toBeInTheDocument();
    expect(screen.getByTestId("route-kaip-tai-veikia")).toBeInTheDocument();
    expect(screen.getByTestId("route-naudojimo-atvejai")).toBeInTheDocument();
    expect(screen.getByTestId("route-duk")).toBeInTheDocument();
    expect(screen.getByTestId("route-apie-mus")).toBeInTheDocument();
    expect(screen.getByTestId("route-contact-sales")).toBeInTheDocument();
    expect(screen.getByTestId("route-*")).toBeInTheDocument(); // 404 puslapis
  });
});
