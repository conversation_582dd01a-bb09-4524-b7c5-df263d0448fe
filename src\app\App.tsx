import { Toaster } from "@/shared/ui/toaster";
import { Toaster as Sonner } from "@/shared/ui/sonner";
import { TooltipProvider } from "@/shared/ui/tooltip";
import { BrowserRouter } from "react-router-dom";
import { ScrollToTop, PageLoader } from "@/shared/layout";
import { AppQueryClientProvider } from "@/shared/contexts";
import AppRouter from "./router";

const App = () => (
  <AppQueryClientProvider>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <ScrollToTop />
        <PageLoader />
        <AppRouter />
      </BrowserRouter>
    </TooltipProvider>
  </AppQueryClientProvider>
);

export default App;
