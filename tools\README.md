# Development Tools

This directory contains utility scripts and development tools.

## Files

### Favicon Generation
- `generate-favicon.bat` - Windows batch script for favicon generation
- `generate-favicon.js` - JavaScript favicon generator
- `generate-favicon.mjs` - ES module favicon generator
- `generate-favicons.js` - Multiple favicon sizes generator
- `png-to-ico.cjs` - CommonJS PNG to ICO converter
- `png-to-ico.mjs` - ES module PNG to ICO converter

## Usage

These tools are used for generating favicons and other development utilities. They can be run independently or as part of the build process.

### Example
```bash
# Generate favicons
node tools/generate-favicons.js

# Convert PNG to ICO
node tools/png-to-ico.mjs
```
