<?xml version="1.0" encoding="UTF-8"?>
<svg width="320" height="80" viewBox="0 0 320 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Logotipo elementai -->
  <g opacity="1">
    <!-- Notion/Linear stiliaus kvadratas su apvaliais kampais -->
    <rect x="40" y="15" width="50" height="50" rx="10" fill="#4285F4">
      <animate attributeName="opacity" values="0;1" dur="0.8s" begin="0s" fill="freeze" />
    </rect>
    
    <!-- Duomenų analizės elementai - linijinis grafikas -->
    <path d="M52 55L60 35L75 45L85 25" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" opacity="1">
      <animate attributeName="opacity" values="0;1" dur="0.5s" begin="0.3s" fill="freeze" />
      <animate attributeName="stroke-dasharray" from="80 80" to="0 80" dur="1.5s" begin="0.3s" fill="freeze" />
      <animate attributeName="stroke-dashoffset" from="80" to="0" dur="1.5s" begin="0.3s" fill="freeze" />
    </path>
    
    <!-- Duomenų taškai -->
    <circle cx="52" cy="55" r="2.5" fill="white" opacity="1">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="0.6s" fill="freeze" />
    </circle>
    
    <circle cx="60" cy="35" r="2.5" fill="white" opacity="1">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="0.7s" fill="freeze" />
    </circle>
    
    <circle cx="75" cy="45" r="2.5" fill="white" opacity="1">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="0.8s" fill="freeze" />
    </circle>
    
    <circle cx="85" cy="25" r="2.5" fill="white" opacity="1">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="0.9s" fill="freeze" />
    </circle>
  </g>
  
  <!-- Tekstas "Roqus Analytics" - Notion/Linear stiliaus šriftas -->
  <g opacity="1">
    <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze" />
    
    <!-- Roqus -->
    <text x="110" y="45" font-family="Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" font-size="28" fill="#4285F4" font-weight="600" letter-spacing="0.2">
      Roqus
    </text>
    
    <!-- Analytics -->
    <text x="190" y="45" font-family="Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" font-size="28" fill="#4285F4" font-weight="400" letter-spacing="0.2">
      Analytics
    </text>
  </g>
</svg>