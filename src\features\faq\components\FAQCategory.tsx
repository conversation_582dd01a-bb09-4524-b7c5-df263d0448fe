import React from "react";
import FAQItem, { FAQItemData } from "./FAQItem";

interface FAQCategoryProps {
  title: string;
  items: FAQItemData[];
  className?: string;
}

/**
 * FAQ klausimų kategorijos komponentas
 * 
 * @param props - Komponent<PERSON> savy<PERSON>
 * @param props.title - Kategorijos pavadinimas
 * @param props.items - FAQ klausimų sąrašas
 * @param props.className - Papildomi CSS klasių pavadinimai
 */
const FAQCategory: React.FC<FAQCategoryProps> = ({ 
  title, 
  items, 
  className = "" 
}) => {
  return (
    <div className={`mb-8 ${className}`}>
      <h2 className="text-3xl font-bold mb-6 text-[#262B30]">
        {title}
      </h2>
      <div className="space-y-4">
        {items.map((item, itemIndex) => (
          <FAQItem
            key={item.value || `item-${itemIndex}`}
            item={item}
          />
        ))}
      </div>
    </div>
  );
};

export default FAQCategory;
