<?xml version="1.0" encoding="UTF-8"?>
<svg width="240" height="60" viewBox="0 0 240 60" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- <PERSON><PERSON>us fonas -->
  <rect width="240" height="60" rx="4" fill="#F5F7FF" opacity="0">
    <animate attributeName="opacity" values="0;1" dur="0.5s" begin="0s" fill="freeze" />
  </rect>
  
  <!-- Logotipo elementai -->
  <g opacity="0">
    <animate attributeName="opacity" values="0;1" dur="0.8s" begin="0.5s" fill="freeze" />
    
    <!-- Notion/Linear stiliaus kvadratas su apvaliais kampais -->
    <rect x="30" y="15" width="30" height="30" rx="6" fill="#4285F4">
      <animate attributeName="opacity" values="0;1" dur="0.8s" begin="0.8s" fill="freeze" />
    </rect>
    
    <!-- Duomenų analizės elementai - linijinis grafikas -->
    <path d="M37 35L42 25L48 30L53 20" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" opacity="0">
      <animate attributeName="opacity" values="0;1" dur="0.5s" begin="1.2s" fill="freeze" />
      <animate attributeName="stroke-dasharray" from="40 40" to="0 40" dur="1.5s" begin="1.2s" fill="freeze" />
      <animate attributeName="stroke-dashoffset" from="40" to="0" dur="1.5s" begin="1.2s" fill="freeze" />
    </path>
    
    <!-- Duomenų taškai -->
    <circle cx="37" cy="35" r="1.5" fill="white" opacity="0">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="1.5s" fill="freeze" />
    </circle>
    
    <circle cx="42" cy="25" r="1.5" fill="white" opacity="0">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="1.6s" fill="freeze" />
    </circle>
    
    <circle cx="48" cy="30" r="1.5" fill="white" opacity="0">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="1.7s" fill="freeze" />
    </circle>
    
    <circle cx="53" cy="20" r="1.5" fill="white" opacity="0">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="1.8s" fill="freeze" />
    </circle>
  </g>
  
  <!-- Tekstas "Roqus Analytics" - Notion/Linear stiliaus šriftas -->
  <g opacity="0">
    <animate attributeName="opacity" values="0;1" dur="1s" begin="2s" fill="freeze" />
    
    <!-- Roqus -->
    <text x="75" y="35" font-family="Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" font-size="20" fill="#4285F4" font-weight="600" letter-spacing="0.2">
      Roqus
    </text>
    
    <!-- Analytics -->
    <text x="135" y="35" font-family="Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" font-size="20" fill="#4285F4" font-weight="400" letter-spacing="0.2">
      Analytics
    </text>
  </g>
</svg>