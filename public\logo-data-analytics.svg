<?xml version="1.0" encoding="UTF-8"?>
<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
    <!-- Apvalus fonas su gradientu -->
    <rect width="48" height="48" rx="12" fill="url(#circleGradient)">
        <!-- Animacija: pulsuojantis efektas -->
        <animate attributeName="opacity" values="0.9;1;0.9" dur="3s" repeatCount="indefinite" />
    </rect>

    <!-- Duomenų vizualizacijos elementai -->
    <g>
        <!-- Skritulinė diagrama (pie chart) -->
        <path d="M24 12 L24 24 L36 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round">
            <animate attributeName="stroke-dasharray" from="40 40" to="0 40" dur="1s" begin="0s" fill="freeze" />
            <animate attributeName="stroke-dashoffset" from="40" to="0" dur="1s" begin="0s" fill="freeze" />
        </path>
        
        <path d="M24 24 L18 14" fill="none" stroke="white" stroke-width="2" stroke-linecap="round">
            <animate attributeName="stroke-dasharray" from="15 15" to="0 15" dur="1s" begin="1s" fill="freeze" />
            <animate attributeName="stroke-dashoffset" from="15" to="0" dur="1s" begin="1s" fill="freeze" />
        </path>
        
        <path d="M24 24 L12 28" fill="none" stroke="white" stroke-width="2" stroke-linecap="round">
            <animate attributeName="stroke-dasharray" from="15 15" to="0 15" dur="1s" begin="1.5s" fill="freeze" />
            <animate attributeName="stroke-dashoffset" from="15" to="0" dur="1s" begin="1.5s" fill="freeze" />
        </path>
        
        <!-- Skritulinės diagramos segmentai -->
        <path d="M24 12 A12 12 0 0 1 36 24" fill="none" stroke="white" stroke-width="2" stroke-opacity="0.8">
            <animate attributeName="stroke-dasharray" from="20 20" to="0 20" dur="1s" begin="0.5s" fill="freeze" />
            <animate attributeName="stroke-dashoffset" from="20" to="0" dur="1s" begin="0.5s" fill="freeze" />
        </path>
        
        <path d="M24 12 A12 12 0 0 0 18 14" fill="none" stroke="white" stroke-width="2" stroke-opacity="0.6">
            <animate attributeName="stroke-dasharray" from="10 10" to="0 10" dur="1s" begin="1.2s" fill="freeze" />
            <animate attributeName="stroke-dashoffset" from="10" to="0" dur="1s" begin="1.2s" fill="freeze" />
        </path>
        
        <path d="M18 14 A12 12 0 0 0 12 28" fill="none" stroke="white" stroke-width="2" stroke-opacity="0.4">
            <animate attributeName="stroke-dasharray" from="20 20" to="0 20" dur="1s" begin="1.7s" fill="freeze" />
            <animate attributeName="stroke-dashoffset" from="20" to="0" dur="1s" begin="1.7s" fill="freeze" />
        </path>
        
        <path d="M12 28 A12 12 0 0 0 36 24" fill="none" stroke="white" stroke-width="2" stroke-opacity="0.2">
            <animate attributeName="stroke-dasharray" from="40 40" to="0 40" dur="1s" begin="2s" fill="freeze" />
            <animate attributeName="stroke-dashoffset" from="40" to="0" dur="1s" begin="2s" fill="freeze" />
        </path>
        
        <!-- Centras -->
        <circle cx="24" cy="24" r="2" fill="white">
            <animate attributeName="r" values="0;2;2.5;2" dur="2s" begin="0s" fill="freeze" />
        </circle>
    </g>
    
    <!-- Duomenų taškai -->
    <g>
        <circle cx="36" cy="24" r="1.5" fill="white">
            <animate attributeName="r" values="0;1.5;2;1.5" dur="1s" begin="0.2s" fill="freeze" />
        </circle>
        <circle cx="18" cy="14" r="1.5" fill="white">
            <animate attributeName="r" values="0;1.5;2;1.5" dur="1s" begin="1.1s" fill="freeze" />
        </circle>
        <circle cx="12" cy="28" r="1.5" fill="white">
            <animate attributeName="r" values="0;1.5;2;1.5" dur="1s" begin="1.6s" fill="freeze" />
        </circle>
    </g>
    
    <!-- Linijinis grafikas apačioje -->
    <g>
        <path d="M12 36 L18 33 L24 35 L30 31 L36 32" stroke="white" stroke-width="2" stroke-linecap="round" fill="none">
            <animate attributeName="stroke-dasharray" from="40 40" to="0 40" dur="1.5s" begin="2.5s" fill="freeze" />
            <animate attributeName="stroke-dashoffset" from="40" to="0" dur="1.5s" begin="2.5s" fill="freeze" />
        </path>
        
        <!-- Duomenų taškai -->
        <circle cx="12" cy="36" r="1.5" fill="white">
            <animate attributeName="r" values="0;1.5;2;1.5" dur="1s" begin="2.5s" fill="freeze" />
        </circle>
        <circle cx="18" cy="33" r="1.5" fill="white">
            <animate attributeName="r" values="0;1.5;2;1.5" dur="1s" begin="2.7s" fill="freeze" />
        </circle>
        <circle cx="24" cy="35" r="1.5" fill="white">
            <animate attributeName="r" values="0;1.5;2;1.5" dur="1s" begin="2.9s" fill="freeze" />
        </circle>
        <circle cx="30" cy="31" r="1.5" fill="white">
            <animate attributeName="r" values="0;1.5;2;1.5" dur="1s" begin="3.1s" fill="freeze" />
        </circle>
        <circle cx="36" cy="32" r="1.5" fill="white">
            <animate attributeName="r" values="0;1.5;2;1.5" dur="1s" begin="3.3s" fill="freeze" />
        </circle>
    </g>
    
    <!-- Duomenų srautas -->
    <g>
        <path d="M24 6 C33.9411 6 42 14.0589 42 24" stroke="white" stroke-width="0.75" stroke-opacity="0.3" stroke-dasharray="2 2">
            <animate attributeName="stroke-dashoffset" values="0;10" dur="10s" repeatCount="indefinite" />
            <animateTransform attributeName="transform" type="rotate" from="0 24 24" to="360 24 24" dur="30s" repeatCount="indefinite" />
        </path>
        
        <path d="M24 8 C32.8366 8 40 15.1634 40 24" stroke="white" stroke-width="0.75" stroke-opacity="0.4" stroke-dasharray="2 2">
            <animate attributeName="stroke-dashoffset" values="0;-10" dur="15s" repeatCount="indefinite" />
            <animateTransform attributeName="transform" type="rotate" from="360 24 24" to="0 24 24" dur="25s" repeatCount="indefinite" />
        </path>
    </g>
    
    <!-- Duomenų binariniai elementai -->
    <g fill="white" opacity="0.6" font-family="monospace" font-size="3">
        <text x="14" y="20" opacity="0.4">
            <animate attributeName="opacity" values="0;0.4;0.6;0.4" dur="3s" begin="3.5s" fill="freeze" />
            01
        </text>
        <text x="32" y="18" opacity="0.4">
            <animate attributeName="opacity" values="0;0.4;0.6;0.4" dur="3s" begin="3.7s" fill="freeze" />
            10
        </text>
        <text x="16" y="38" opacity="0.4">
            <animate attributeName="opacity" values="0;0.4;0.6;0.4" dur="3s" begin="3.9s" fill="freeze" />
            01
        </text>
        <text x="30" y="38" opacity="0.4">
            <animate attributeName="opacity" values="0;0.4;0.6;0.4" dur="3s" begin="4.1s" fill="freeze" />
            10
        </text>
    </g>
    
    <!-- RA raidės -->
    <g>
        <!-- R raidė -->
        <path d="M18 42 L18 36 L21 36 C21.6 36 22 36.4 22 37 C22 37.6 21.6 38 21 38 L22 38 L23 42" 
              stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" fill="none">
            <animate attributeName="stroke-dasharray" from="20 20" to="0 20" dur="1.5s" begin="4.5s" fill="freeze" />
            <animate attributeName="stroke-dashoffset" from="20" to="0" dur="1.5s" begin="4.5s" fill="freeze" />
        </path>
        
        <!-- A raidė -->
        <path d="M25 42 L27 36 L29 42 M25.5 40 L28.5 40" 
              stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" fill="none">
            <animate attributeName="stroke-dasharray" from="20 20" to="0 20" dur="1.5s" begin="5s" fill="freeze" />
            <animate attributeName="stroke-dashoffset" from="20" to="0" dur="1.5s" begin="5s" fill="freeze" />
        </path>
    </g>

    <!-- Gradiento apibrėžimai -->
    <defs>
        <linearGradient id="circleGradient" x1="0" y1="0" x2="48" y2="48" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#A78BFA">
                <animate attributeName="stop-color" values="#A78BFA;#8B5CF6;#A78BFA" dur="6s" repeatCount="indefinite" />
            </stop>
            <stop offset="1" stop-color="#7C3AED">
                <animate attributeName="stop-color" values="#7C3AED;#6D28D9;#7C3AED" dur="6s" repeatCount="indefinite" />
            </stop>
        </linearGradient>
    </defs>
</svg>