<!DOCTYPE html>
<html lang="lt">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Roqus Analytics Favicon Eksportas</title>
  <style>
    body {
      font-family: 'Inter', sans-serif;
      margin: 0;
      padding: 0;
      background: radial-gradient(circle, #0a0a20 0%, #050510 100%);
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #ffffff;
      padding: 2rem;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
      text-align: center;
      background: rgba(20, 20, 40, 0.5);
      backdrop-filter: blur(10px);
      border-radius: 1rem;
      box-shadow: 0 4px 30px rgba(155, 109, 255, 0.2), 
                  0 0 10px rgba(148, 210, 255, 0.2),
                  inset 0 0 5px rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(155, 109, 255, 0.2);
    }
    
    h1 {
      font-size: 2rem;
      margin-bottom: 1.5rem;
      font-weight: 700;
      background: linear-gradient(to right, #94D2FF, #9B6DFF);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      text-shadow: 0 0 20px rgba(155, 109, 255, 0.3);
    }
    
    p {
      font-size: 1rem;
      margin-bottom: 2rem;
      color: #a0a0c0;
    }
    
    .sizes-container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }
    
    .size-box {
      background: rgba(10, 10, 30, 0.5);
      border-radius: 0.5rem;
      padding: 1rem;
      box-shadow: 0 4px 20px rgba(155, 109, 255, 0.1);
      text-align: center;
      border: 1px solid rgba(155, 109, 255, 0.1);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      height: 100%;
    }
    
    .size-title {
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: #94D2FF;
    }
    
    .favicon-container {
      background: #050510;
      border-radius: 0.5rem;
      padding: 1rem;
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .button {
      display: inline-block;
      background: linear-gradient(to right, #9B6DFF, #7C3AED);
      color: white;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: 0.5rem;
      text-decoration: none;
      transition: all 0.3s ease;
      border: none;
      cursor: pointer;
      font-size: 0.875rem;
      position: relative;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(155, 109, 255, 0.3);
    }
    
    .button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(155, 109, 255, 0.5);
    }
    
    .instructions {
      background: rgba(10, 10, 30, 0.5);
      border-radius: 0.5rem;
      padding: 1.5rem;
      margin-bottom: 2rem;
      text-align: left;
      border: 1px solid rgba(155, 109, 255, 0.1);
    }
    
    .instructions h2 {
      color: #94D2FF;
      margin-top: 0;
      font-size: 1.25rem;
      margin-bottom: 1rem;
    }
    
    .instructions ol {
      margin: 0;
      padding-left: 1.5rem;
    }
    
    .instructions li {
      margin-bottom: 0.5rem;
      color: #a0a0c0;
    }
    
    .instructions code {
      background: rgba(155, 109, 255, 0.1);
      padding: 0.2rem 0.4rem;
      border-radius: 0.25rem;
      font-family: monospace;
      color: #94D2FF;
    }
    
    canvas {
      display: block;
      margin: 0 auto;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Roqus Analytics Favicon Eksportas</h1>
    <p>Šis puslapis leidžia eksportuoti faviconą įvairiais dydžiais. Tiesiog spauskite "Atsisiųsti" mygtuką prie kiekvieno dydžio.</p>
    
    <div class="instructions">
      <h2>Instrukcijos</h2>
      <ol>
        <li>Spauskite "Atsisiųsti" mygtuką prie kiekvieno dydžio, kurį norite išsaugoti.</li>
        <li>Išsaugokite failus <code>public</code> kataloge.</li>
        <li>Įsitikinkite, kad failų pavadinimai atitinka <code>favicon-16x16.png</code>, <code>favicon-32x32.png</code> ir t.t.</li>
        <li>Favicon.svg failas jau yra sukurtas ir išsaugotas <code>public</code> kataloge.</li>
        <li>Atnaujinkite <code>index.html</code> failą, kad jis naudotų naujus faviconus (jau atlikta).</li>
      </ol>
    </div>
    
    <div class="sizes-container">
      <div class="size-box">
        <div class="size-title">16x16</div>
        <div class="favicon-container">
          <canvas id="canvas-16" width="16" height="16"></canvas>
        </div>
        <button class="button" onclick="downloadFavicon(16)">Atsisiųsti</button>
      </div>
      
      <div class="size-box">
        <div class="size-title">32x32</div>
        <div class="favicon-container">
          <canvas id="canvas-32" width="32" height="32"></canvas>
        </div>
        <button class="button" onclick="downloadFavicon(32)">Atsisiųsti</button>
      </div>
      
      <div class="size-box">
        <div class="size-title">48x48</div>
        <div class="favicon-container">
          <canvas id="canvas-48" width="48" height="48"></canvas>
        </div>
        <button class="button" onclick="downloadFavicon(48)">Atsisiųsti</button>
      </div>
      
      <div class="size-box">
        <div class="size-title">64x64</div>
        <div class="favicon-container">
          <canvas id="canvas-64" width="64" height="64"></canvas>
        </div>
        <button class="button" onclick="downloadFavicon(64)">Atsisiųsti</button>
      </div>
      
      <div class="size-box">
        <div class="size-title">128x128</div>
        <div class="favicon-container">
          <canvas id="canvas-128" width="128" height="128"></canvas>
        </div>
        <button class="button" onclick="downloadFavicon(128)">Atsisiųsti</button>
      </div>
      
      <div class="size-box">
        <div class="size-title">192x192</div>
        <div class="favicon-container">
          <canvas id="canvas-192" width="192" height="192"></canvas>
        </div>
        <button class="button" onclick="downloadFavicon(192)">Atsisiųsti</button>
      </div>
      
      <div class="size-box">
        <div class="size-title">512x512</div>
        <div class="favicon-container" style="max-width: 150px; max-height: 150px;">
          <canvas id="canvas-512" width="512" height="512" style="max-width: 100%; max-height: 100%;"></canvas>
        </div>
        <button class="button" onclick="downloadFavicon(512)">Atsisiųsti</button>
      </div>
      
      <div class="size-box">
        <div class="size-title">Apple Touch Icon</div>
        <div class="favicon-container" style="max-width: 150px; max-height: 150px;">
          <canvas id="canvas-180" width="180" height="180" style="max-width: 100%; max-height: 100%;"></canvas>
        </div>
        <button class="button" onclick="downloadFavicon(180, 'apple-touch-icon')">Atsisiųsti</button>
      </div>
    </div>
  </div>
  
  <script>
    // Piešiame faviconą į canvas
    function drawFavicon(size) {
      const canvas = document.getElementById(`canvas-${size}`);
      const ctx = canvas.getContext('2d');
      
      // Fonas
      ctx.fillStyle = '#050510';
      ctx.beginPath();
      ctx.rect(0, 0, size, size);
      ctx.fill();
      
      // Skalės faktorius
      const scale = size / 512;
      
      // Holograminis tinklelis
      ctx.strokeStyle = '#00FFFF';
      ctx.globalAlpha = 0.3;
      ctx.lineWidth = Math.max(1 * scale, 0.5);
      
      // Horizontali linija
      ctx.beginPath();
      ctx.moveTo(size * 0.1, size * 0.5);
      ctx.lineTo(size * 0.9, size * 0.5);
      ctx.stroke();
      
      // Vertikali linija
      ctx.beginPath();
      ctx.moveTo(size * 0.5, size * 0.1);
      ctx.lineTo(size * 0.5, size * 0.9);
      ctx.stroke();
      
      // Stulpeliai
      ctx.globalAlpha = 1;
      const barPositions = [
        { x: 0.19, y: 0.5, width: 0.08, height: 0.23 },
        { x: 0.34, y: 0.42, width: 0.08, height: 0.31 },
        { x: 0.5, y: 0.58, width: 0.08, height: 0.15 },
        { x: 0.66, y: 0.34, width: 0.08, height: 0.39 },
        { x: 0.81, y: 0.46, width: 0.08, height: 0.27 }
      ];
      
      // Piešiame stulpelius
      barPositions.forEach(bar => {
        const x = bar.x * size;
        const y = bar.y * size;
        const width = bar.width * size;
        const height = bar.height * size;
        
        // Pagrindinis stulpelis
        ctx.fillStyle = '#94D2FF';
        ctx.beginPath();
        ctx.rect(x, y, width, height);
        ctx.fill();
        
        // Švytintis efektas
        ctx.fillStyle = '#5CEFFF';
        ctx.globalAlpha = 0.5;
        ctx.beginPath();
        ctx.rect(x, y, width, height);
        ctx.fill();
        ctx.globalAlpha = 1;
      });
      
      // Linija virš stulpelių
      ctx.strokeStyle = '#9B6DFF';
      ctx.lineWidth = Math.max(4 * scale, 1);
      ctx.lineCap = 'round';
      ctx.beginPath();
      ctx.moveTo(barPositions[0].x * size + (barPositions[0].width * size / 2), barPositions[0].y * size);
      
      for (let i = 1; i < barPositions.length; i++) {
        const x = barPositions[i].x * size + (barPositions[i].width * size / 2);
        const y = barPositions[i].y * size;
        ctx.lineTo(x, y);
      }
      
      ctx.stroke();
      
      // Švytinti linija
      ctx.strokeStyle = '#B026FF';
      ctx.lineWidth = Math.max(2 * scale, 0.5);
      ctx.globalAlpha = 0.7;
      ctx.beginPath();
      ctx.moveTo(barPositions[0].x * size + (barPositions[0].width * size / 2), barPositions[0].y * size);
      
      for (let i = 1; i < barPositions.length; i++) {
        const x = barPositions[i].x * size + (barPositions[i].width * size / 2);
        const y = barPositions[i].y * size;
        ctx.lineTo(x, y);
      }
      
      ctx.stroke();
      ctx.globalAlpha = 1;
      
      // Taškai linijos viršūnėse
      barPositions.forEach(bar => {
        const x = bar.x * size + (bar.width * size / 2);
        const y = bar.y * size;
        const radius = Math.max(3 * scale, 1);
        
        // Pagrindinis taškas
        ctx.fillStyle = '#7C3AED';
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, Math.PI * 2);
        ctx.fill();
        
        // Švytintis efektas
        ctx.fillStyle = '#B026FF';
        ctx.globalAlpha = 0.5;
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, Math.PI * 2);
        ctx.fill();
        ctx.globalAlpha = 1;
      });
    }
    
    // Atsisiųsti faviconą
    function downloadFavicon(size, prefix = 'favicon') {
      const canvas = document.getElementById(`canvas-${size}`);
      const link = document.createElement('a');
      link.download = `${prefix}${prefix === 'favicon' ? `-${size}x${size}` : ''}.png`;
      link.href = canvas.toDataURL('image/png');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
    
    // Piešiame visus faviconus
    window.onload = function() {
      [16, 32, 48, 64, 128, 192, 512, 180].forEach(size => {
        drawFavicon(size);
      });
    };
  </script>
</body>
</html>