# Global Assets

This directory contains global assets used across the application.

## Structure

### 🎨 styles/
Global CSS files and style modules:
- `index.css` - Main global stylesheet
- `Animations.module.css` - Animation utilities
- `Button.module.css` - Button component styles
- `Card.module.css` - Card component styles

### 🔤 fonts/
Custom fonts and typography assets (currently empty)

### 🎯 icons/
Global icons and icon assets (currently empty)

## Usage

### Styles
Global styles are imported in `src/main.tsx`:
```typescript
import "../assets/styles/index.css";
```

CSS modules can be imported in components:
```typescript
import styles from "../../assets/styles/Button.module.css";
```

### Adding New Assets
- Add global fonts to `fonts/`
- Add global icons to `icons/`
- Add global styles to `styles/`
