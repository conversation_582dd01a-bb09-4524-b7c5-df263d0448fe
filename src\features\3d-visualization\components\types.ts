import { BlendFunction } from "postprocessing";

/**
 * Bloom efekto konfigūracija
 */
export interface BloomConfig {
  /** Efekto intensyvumas (0-3) */
  intensity?: number;
  /** Šviesumo slenkstis (0-1) */
  luminanceThreshold?: number;
  /** Šviesumo i<PERSON>lyginima<PERSON> (0-1) */
  luminanceSmoothing?: number;
  /** Efekto aukštis pikseliais */
  height?: number;
  /** Maišymo funkcija */
  blendFunction?: BlendFunction;
}

/**
 * Chromatinės aberacijos efekto konfigūracija
 */
export interface ChromaticAberrationConfig {
  /** Poslinkis [x, y] */
  offset?: [number, number];
  /** Efekto nepermatomumas (0-1) */
  opacity?: number;
  /** Maišymo funkcija */
  blendFunction?: BlendFunction;
}

/**
 * Triukšmo efekto konfigūracija
 */
export interface NoiseConfig {
  /** Efekto nepermatomumas (0-1) */
  opacity?: number;
  /** Maišymo funkcija */
  blendFunction?: BlendFunction;
}

/**
 * OrbitControls komponento konfigūracija
 */
export interface OrbitControlsConfig {
  /** Ar leidžiama slinkti kamerą */
  enablePan?: boolean;
  /** Ar leidžiama keisti mastelį */
  enableZoom?: boolean;
  /** Minimalus poliaus kampas (radianais) */
  minPolarAngle?: number;
  /** Maksimalus poliaus kampas (radianais) */
  maxPolarAngle?: number;
  /** Ar įjungtas automatinis sukimasis */
  autoRotate?: boolean;
  /** Automatinio sukimosi greitis */
  autoRotateSpeed?: number;
}

/**
 * Visų post-processing efektų konfigūracija
 */
export interface PostProcessingConfig {
  /** Bloom efekto konfigūracija */
  bloom?: BloomConfig;
  /** Chromatinės aberacijos efekto konfigūracija */
  chromaticAberration?: ChromaticAberrationConfig;
  /** Triukšmo efekto konfigūracija */
  noise?: NoiseConfig;
}

/**
 * Numatytosios konfigūracijos
 */
export const DEFAULT_BLOOM_CONFIG: BloomConfig = {
  intensity: 1.5,
  luminanceThreshold: 0.2,
  luminanceSmoothing: 0.9,
  height: 300,
  blendFunction: BlendFunction.SCREEN
};

export const DEFAULT_CHROMATIC_ABERRATION_CONFIG: ChromaticAberrationConfig = {
  offset: [0.002, 0.002],
  opacity: 0.3,
  blendFunction: BlendFunction.NORMAL
};

export const DEFAULT_NOISE_CONFIG: NoiseConfig = {
  opacity: 0.05,
  blendFunction: BlendFunction.OVERLAY
};

export const DEFAULT_ORBIT_CONTROLS_CONFIG: OrbitControlsConfig = {
  enablePan: false,
  enableZoom: false,
  minPolarAngle: Math.PI / 4,
  maxPolarAngle: Math.PI / 2,
  autoRotate: true,
  autoRotateSpeed: 0.5
};

export const DEFAULT_POST_PROCESSING_CONFIG: PostProcessingConfig = {
  bloom: DEFAULT_BLOOM_CONFIG,
  chromaticAberration: DEFAULT_CHROMATIC_ABERRATION_CONFIG,
  noise: DEFAULT_NOISE_CONFIG
};
