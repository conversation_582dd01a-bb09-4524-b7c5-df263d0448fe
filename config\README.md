# Configuration Files

This directory contains all configuration files organized by category.

## Structure

### 🏗️ build/
Build and bundling configurations:
- `postcss.config.js` - PostCSS configuration for CSS processing
- `tailwind.config.js` - Tailwind CSS configuration
- `components.json` - shadcn/ui components configuration

### 🛠️ dev/
Development and testing configurations:
- `eslint.config.js` - ESLint configuration for code linting
- `vitest.config.ts` - Vitest configuration for testing

### 📝 typescript/
TypeScript configurations:
- `tsconfig.app.json` - TypeScript config for application code
- `tsconfig.node.json` - TypeScript config for Node.js scripts

## Usage

These configurations are automatically used by their respective tools. The main `tsconfig.json` and `vite.config.ts` in the root directory reference these files.
