<!DOCTYPE html>
<html lang="lt">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Roqus Analytics Futuristinis 3D Favicon</title>
  <style>
    body {
      font-family: 'Inter', sans-serif;
      margin: 0;
      padding: 0;
      background: radial-gradient(circle, #0a0a20 0%, #050510 100%);
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #ffffff;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
      text-align: center;
    }
    
    h1 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      background: linear-gradient(to right, #94D2FF, #9B6DFF);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      display: inline-block;
    }
    
    p {
      font-size: 1.1rem;
      color: #a0a0c0;
      max-width: 800px;
      margin: 0 auto 2rem auto;
    }
    
    .preview-container {
      display: flex;
      flex-wrap: wrap;
      gap: 2rem;
      justify-content: center;
      margin-bottom: 3rem;
    }
    
    .preview-box {
      background: rgba(20, 20, 40, 0.5);
      backdrop-filter: blur(10px);
      border-radius: 1rem;
      padding: 1.5rem;
      box-shadow: 0 4px 30px rgba(155, 109, 255, 0.2), 
                  0 0 10px rgba(148, 210, 255, 0.2),
                  inset 0 0 5px rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(155, 109, 255, 0.2);
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    
    .preview-title {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: #94D2FF;
    }
    
    .favicon-container {
      width: 300px;
      height: 300px;
      position: relative;
      margin-bottom: 1rem;
      border-radius: 0.5rem;
      overflow: hidden;
      box-shadow: 0 0 30px rgba(148, 210, 255, 0.3);
    }
    
    .favicon-iframe {
      width: 100%;
      height: 100%;
      border: none;
      border-radius: 0.5rem;
      background-color: transparent;
    }
    
    .favicon-image {
      display: block;
      max-width: 100%;
      border-radius: 0.5rem;
    }
    
    .sizes-container {
      display: flex;
      flex-wrap: wrap;
      gap: 2rem;
      justify-content: center;
      margin-bottom: 3rem;
    }
    
    .size-box {
      background: rgba(20, 20, 40, 0.5);
      backdrop-filter: blur(10px);
      border-radius: 0.5rem;
      padding: 1rem;
      box-shadow: 0 4px 30px rgba(155, 109, 255, 0.1);
      border: 1px solid rgba(155, 109, 255, 0.2);
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    
    .size-title {
      font-size: 0.9rem;
      margin-bottom: 0.5rem;
      color: #94D2FF;
    }
    
    .button {
      display: inline-block;
      background: linear-gradient(to right, #7C3AED, #9B6DFF);
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s ease;
      border: none;
      cursor: pointer;
      box-shadow: 0 4px 15px rgba(155, 109, 255, 0.3);
    }
    
    .button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(155, 109, 255, 0.4);
    }
    
    h2 {
      font-size: 2rem;
      margin: 2rem 0 1.5rem 0;
      color: #94D2FF;
    }
    
    .features-list {
      text-align: left;
      max-width: 800px;
      margin: 0 auto 3rem auto;
      background: rgba(20, 20, 40, 0.5);
      backdrop-filter: blur(10px);
      border-radius: 1rem;
      padding: 2rem;
      box-shadow: 0 4px 30px rgba(155, 109, 255, 0.2);
      border: 1px solid rgba(155, 109, 255, 0.2);
    }
    
    .features-list h2 {
      color: #94D2FF;
      margin-top: 0;
      font-size: 1.75rem;
      text-align: center;
      margin-bottom: 1.5rem;
    }
    
    .features-list ul {
      padding-left: 1.5rem;
      margin: 0;
    }
    
    .features-list li {
      margin-bottom: 0.75rem;
      color: #a0a0c0;
    }
    
    .features-list li strong {
      color: #9B6DFF;
    }
    
    .glow {
      animation: glow 2s infinite alternate;
    }
    
    @keyframes glow {
      from {
        box-shadow: 0 0 10px rgba(148, 210, 255, 0.3);
      }
      to {
        box-shadow: 0 0 30px rgba(148, 210, 255, 0.7);
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Roqus Analytics Futuristinis 3D Favicon</h1>
    <p>Modernus, futuristinis 3D faviconas su "wow" efektais, atspindintis duomenų analitiką ir vizualizaciją. Sukurtas naudojant Three.js, React Three Fiber ir pažangius vizualinius efektus.</p>
    
    <div class="preview-container">
      <div class="preview-box">
        <div class="preview-title">3D Interaktyvi Peržiūra</div>
        <div class="favicon-container glow">
          <iframe id="favicon-iframe" class="favicon-iframe" src="about:blank"></iframe>
        </div>
        <p>Interaktyvi 3D versija su animacijomis</p>
      </div>
      
      <div class="preview-box">
        <div class="preview-title">Statinė Peržiūra</div>
        <div class="favicon-container">
          <img src="favicon-original.png" alt="Roqus Analytics Favicon" class="favicon-image" style="max-width: 100%; max-height: 100%;">
        </div>
        <p>Statinė PNG versija su visais efektais</p>
      </div>
    </div>
    
    <div class="features-list">
      <h2>Futuristiniai Efektai</h2>
      <ul>
        <li><strong>3D Grafikas</strong> - Interaktyvus 3D grafikas su stulpeliais ir linija, atspindintis duomenų analitiką</li>
        <li><strong>Neoniniai Švytėjimai</strong> - Neoniniai švytėjimai ir šviesos efektai, suteikiantys futuristinį vaizdą</li>
        <li><strong>Holograminiai Elementai</strong> - Holograminiai tinkleliai ir permatomos detalės</li>
        <li><strong>Animacijos</strong> - Pulsuojančios animacijos ir dinamiški elementai</li>
        <li><strong>Post-Processing</strong> - Pažangūs post-processing efektai: Bloom, ChromaticAberration, Noise</li>
        <li><strong>Interaktyvumas</strong> - Galimybė sukti ir apžiūrėti faviconą iš visų pusių</li>
      </ul>
    </div>
    
    <h2>Skirtingi Dydžiai</h2>
    <div class="sizes-container">
      <div class="size-box">
        <div class="size-title">16x16</div>
        <img src="favicon-16x16.png" alt="16x16" width="16" height="16">
      </div>
      <div class="size-box">
        <div class="size-title">32x32</div>
        <img src="favicon-32x32.png" alt="32x32" width="32" height="32">
      </div>
      <div class="size-box">
        <div class="size-title">48x48</div>
        <img src="favicon-48x48.png" alt="48x48" width="48" height="48">
      </div>
      <div class="size-box">
        <div class="size-title">64x64</div>
        <img src="favicon-64x64.png" alt="64x64" width="64" height="64">
      </div>
      <div class="size-box">
        <div class="size-title">128x128</div>
        <img src="favicon-128x128.png" alt="128x128" width="128" height="128">
      </div>
    </div>
    
    <div style="margin-top: 3rem;">
      <a href="/" class="button">Grįžti į Pagrindinį Puslapį</a>
    </div>
  </div>
  
  <script>
    // Dinamiškai sukuriame iframe turinį
    window.onload = function() {
      const iframe = document.getElementById('favicon-iframe');
      const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
      
      iframeDoc.open();
      iframeDoc.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>3D Favicon</title>
            <script type="importmap">
              {
                "imports": {
                  "three": "https://unpkg.com/three@0.176.0/build/three.module.js",
                  "three/addons/": "https://unpkg.com/three@0.176.0/examples/jsm/",
                  "react": "https://esm.sh/react@18.3.1",
                  "react-dom": "https://esm.sh/react-dom@18.3.1",
                  "@react-three/fiber": "https://esm.sh/@react-three/fiber@9.1.2",
                  "@react-three/drei": "https://esm.sh/@react-three/drei@10.0.8",
                  "@react-three/postprocessing": "https://esm.sh/@react-three/postprocessing@3.0.4",
                  "postprocessing": "https://esm.sh/postprocessing@6.33.4",
                  "@react-spring/three": "https://esm.sh/@react-spring/three@10.0.0"
                }
              }
            <\/script>
            <style>
              body, html {
                margin: 0;
                padding: 0;
                overflow: hidden;
                width: 100%;
                height: 100%;
                background: radial-gradient(circle, #0a0a20 0%, #050510 100%);
              }
              #root {
                width: 100%;
                height: 100%;
              }
            </style>
          <body>
            <div id="root"></div>
            <script type="module">
              import React, { useRef, useMemo, useState, useEffect, Suspense } from 'react';
              import ReactDOM from 'react-dom/client';
              import { Canvas, useFrame } from '@react-three/fiber';
              import { 
                OrbitControls, 
                PerspectiveCamera, 
                Environment, 
                ContactShadows,
                Float,
                Trail,
                Sparkles
              } from '@react-three/drei';
              import { 
                EffectComposer, 
                Bloom, 
                ChromaticAberration, 
                Noise 
              } from '@react-three/postprocessing';
              import { BlendFunction } from 'postprocessing';
              import { useSpring, animated, config } from '@react-spring/three';
              import * as THREE from 'three';
              
              // Spalvos iš dizaino dokumentacijos su futuristiniais atspalviais
              const BLUE_COLOR = '#94D2FF'; // rgba(148, 210, 255, 1)
              const BLUE_GLOW = '#5CEFFF'; // Ryškesnė mėlyna švytėjimui
              const PURPLE_COLOR = '#9B6DFF';
              const DARK_PURPLE_COLOR = '#7C3AED';
              const NEON_BLUE = '#00FFFF';
              const NEON_PURPLE = '#B026FF';
              
              // Animuotas grafikas
              const AnimatedChart = () => {
                const group = useRef(null);
                const floatingGroup = useRef(null);
                const [hovered, setHovered] = useState(false);
                
                // Animacijos su react-spring
                const { rotation } = useSpring({
                  from: { rotation: [0, 0, 0] },
                  to: { rotation: [0, Math.PI * 2, 0] },
                  config: { duration: 20000 },
                  loop: true,
                });
              
                // Hover animacija
                const { scale } = useSpring({
                  scale: hovered ? 1.1 : 1,
                  config: config.wobbly,
                });
              
                // Pulsuojantis efektas
                const [pulseIntensity, setPulseIntensity] = useState(0);
                useEffect(() => {
                  const interval = setInterval(() => {
                    setPulseIntensity((prev) => (prev + 0.1) % 1);
                  }, 100);
                  return () => clearInterval(interval);
                }, []);
              
                // Grafiko duomenys su dinamiškesniais aukščiais
                const barData = useMemo(() => [
                  { height: 0.6, position: [-0.6, 0.3, 0], speed: 0.3 },
                  { height: 1.0, position: [-0.3, 0.5, 0], speed: 0.5 },
                  { height: 0.5, position: [0, 0.25, 0], speed: 0.7 },
                  { height: 1.2, position: [0.3, 0.6, 0], speed: 0.4 },
                  { height: 0.8, position: [0.6, 0.4, 0], speed: 0.6 },
                ], []);
              
                // Linijos taškai
                const linePoints = useMemo(() => {
                  const points = [];
                  for (const bar of barData) {
                    points.push(
                      new THREE.Vector3(
                        bar.position[0], 
                        bar.height, 
                        bar.position[2]
                      )
                    );
                  }
                  return points;
                }, [barData]);
              
                // Linijos geometrija
                const lineGeometry = useMemo(() => {
                  const curve = new THREE.CatmullRomCurve3(linePoints);
                  return new THREE.BufferGeometry().setFromPoints(
                    curve.getPoints(50)
                  );
                }, [linePoints]);
              
                // Holograminis efektas - tinklelis
                const gridSize = 2;
                const gridDivisions = 20;
                const gridColor = new THREE.Color(NEON_BLUE);
              
                // Animacija
                useFrame((state) => {
                  if (group.current) {
                    // Pagrindinis sukimasis
                    group.current.rotation.y = state.clock.getElapsedTime() * 0.2;
                    
                    // Papildomas svyravimas
                    group.current.rotation.x = Math.sin(state.clock.getElapsedTime() * 0.5) * 0.1;
                    
                    // Dinamiškas stulpelių aukštis
                    barData.forEach((bar, index) => {
                      if (group.current?.children[index]) {
                        const mesh = group.current.children[index];
                        const newHeight = bar.height + Math.sin(state.clock.getElapsedTime() * bar.speed) * 0.1;
                        mesh.scale.y = newHeight / bar.height;
                        mesh.position.y = newHeight / 2;
                      }
                    });
                  }
                  
                  if (floatingGroup.current) {
                    floatingGroup.current.position.y = Math.sin(state.clock.getElapsedTime()) * 0.05;
                  }
                });
              
                return (
                  <animated.group 
                    ref={group} 
                    rotation={rotation} 
                    scale={scale}
                    onPointerOver={() => setHovered(true)}
                    onPointerOut={() => setHovered(false)}
                  >
                    {/* Holograminis tinklelis */}
                    <gridHelper 
                      args={[gridSize, gridDivisions, gridColor, gridColor]} 
                      position={[0, -0.05, 0]} 
                      rotation={[0, 0, 0]}
                    >
                      <meshBasicMaterial 
                        color={NEON_BLUE} 
                        transparent 
                        opacity={0.3} 
                        blending={THREE.AdditiveBlending}
                      />
                    </gridHelper>
                    
                    {/* Stulpeliai su futuristiniais efektais */}
                    {barData.map((bar, index) => (
                      <group key={index}>
                        <mesh 
                          position={[bar.position[0], bar.height / 2, bar.position[2]]}
                          castShadow
                        >
                          <boxGeometry args={[0.15, bar.height, 0.15]} />
                          <meshStandardMaterial
                            color={BLUE_COLOR}
                            metalness={0.8}
                            roughness={0.1}
                            emissive={BLUE_GLOW}
                            emissiveIntensity={0.5 + pulseIntensity * 0.5}
                          />
                        </mesh>
                        
                        {/* Švytintis efektas aplink stulpelius */}
                        <mesh 
                          position={[bar.position[0], bar.height / 2, bar.position[2]]}
                          scale={[1.2, 1.2, 1.2]}
                        >
                          <boxGeometry args={[0.15, bar.height, 0.15]} />
                          <meshBasicMaterial 
                            color={BLUE_GLOW} 
                            transparent 
                            opacity={0.2} 
                            blending={THREE.AdditiveBlending}
                          />
                        </mesh>
                      </group>
                    ))}
              
                    {/* Linija virš stulpelių su švytėjimu */}
                    <group ref={floatingGroup}>
                      <line geometry={lineGeometry}>
                        <lineBasicMaterial 
                          color={NEON_PURPLE} 
                          linewidth={2} 
                          blending={THREE.AdditiveBlending}
                        />
                      </line>
                      
                      {/* Švytinti linija */}
                      <line geometry={lineGeometry}>
                        <lineBasicMaterial 
                          color={PURPLE_COLOR} 
                          linewidth={4} 
                          transparent 
                          opacity={0.5} 
                          blending={THREE.AdditiveBlending}
                        />
                      </line>
              
                      {/* Apskritimas kiekviename linijos taške su švytėjimu */}
                      {linePoints.map((point, index) => (
                        <group key={\`point-\${index}\`}>
                          <mesh position={[point.x, point.y, point.z]}>
                            <sphereGeometry args={[0.05, 16, 16]} />
                            <meshStandardMaterial 
                              color={DARK_PURPLE_COLOR} 
                              emissive={NEON_PURPLE}
                              emissiveIntensity={0.8 + pulseIntensity * 0.5}
                              metalness={1}
                              roughness={0}
                            />
                          </mesh>
                          
                          {/* Švytintis efektas aplink tašką */}
                          <mesh 
                            position={[point.x, point.y, point.z]}
                            scale={[2, 2, 2]}
                          >
                            <sphereGeometry args={[0.05, 16, 16]} />
                            <meshBasicMaterial 
                              color={NEON_PURPLE} 
                              transparent 
                              opacity={0.3} 
                              blending={THREE.AdditiveBlending}
                            />
                          </mesh>
                        </group>
                      ))}
                    </group>
              
                    {/* Dalelių sistema aplink grafiką */}
                    <Sparkles 
                      count={50} 
                      scale={[2, 2, 2]} 
                      size={0.5} 
                      speed={0.3} 
                      color={NEON_BLUE} 
                    />
                    
                    {/* Futuristinis pagrindas */}
                    <mesh 
                      position={[0, -0.05, 0]} 
                      rotation={[-Math.PI / 2, 0, 0]}
                      receiveShadow
                    >
                      <planeGeometry args={[2, 2]} />
                      <meshStandardMaterial 
                        color="#101020" 
                        transparent
                        opacity={0.7}
                        metalness={0.9}
                        roughness={0.1}
                        emissive={BLUE_GLOW}
                        emissiveIntensity={0.2 + pulseIntensity * 0.2}
                      />
                    </mesh>
                    
                    {/* Holograminis cilindras aplink visą grafiką */}
                    <mesh position={[0, 0.5, 0]} rotation={[0, 0, 0]}>
                      <cylinderGeometry args={[1.2, 1.2, 2, 32, 1, true]} />
                      <meshBasicMaterial 
                        color={NEON_BLUE} 
                        transparent 
                        opacity={0.1} 
                        blending={THREE.AdditiveBlending}
                        side={THREE.DoubleSide}
                        wireframe={true}
                      />
                    </mesh>
                  </animated.group>
                );
              };
              
              // Pagrindinis komponentas
              const Favicon3D = () => {
                return (
                  <Canvas shadows dpr={[1, 2]}>
                    <Suspense fallback={null}>
                      <PerspectiveCamera makeDefault position={[0, 1.5, 3]} fov={40} />
                      
                      {/* Apšvietimas */}
                      <ambientLight intensity={0.2} />
                      <spotLight 
                        position={[5, 5, 5]} 
                        angle={0.15} 
                        penumbra={1} 
                        intensity={0.8} 
                        castShadow 
                        color="#ffffff"
                      />
                      <pointLight position={[-5, 5, -5]} intensity={0.5} color="#5CEFFF" />
                      <pointLight position={[5, -5, 5]} intensity={0.5} color="#B026FF" />
                      
                      {/* Pagrindinis 3D grafikas */}
                      <AnimatedChart />
                      
                      {/* Šešėliai */}
                      <ContactShadows 
                        position={[0, -0.05, 0]} 
                        opacity={0.5} 
                        scale={10} 
                        blur={1.5} 
                        far={10} 
                        color="#000000"
                      />
                      
                      {/* Aplinka */}
                      <Environment preset="night" />
                      
                      {/* Post-processing efektai */}
                      <EffectComposer>
                        {/* Bloom efektas švytėjimui */}
                        <Bloom 
                          intensity={1.5} 
                          luminanceThreshold={0.2} 
                          luminanceSmoothing={0.9} 
                          height={300}
                          blendFunction={BlendFunction.SCREEN}
                        />
                        
                        {/* Chromatinė aberacija kraštams */}
                        <ChromaticAberration 
                          offset={[0.002, 0.002]} 
                          blendFunction={BlendFunction.NORMAL}
                          opacity={0.3}
                        />
                        
                        {/* Subtilus triukšmas holograminiam efektui */}
                        <Noise 
                          opacity={0.05} 
                          blendFunction={BlendFunction.OVERLAY}
                        />
                      </EffectComposer>
                      
                      {/* Kameros kontrolės */}
                      <OrbitControls 
                        enablePan={false}
                        enableZoom={false}
                        minPolarAngle={Math.PI / 4}
                        maxPolarAngle={Math.PI / 2}
                      />
                    </Suspense>
                  </Canvas>
                );
              };
              
              // Renderis
              ReactDOM.createRoot(document.getElementById('root')).render(<Favicon3D />);
            </script>
          </body>
        </html>
      `);
      iframeDoc.close();
    };
  </script>
</body>
</html>