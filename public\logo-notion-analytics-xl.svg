<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="100" viewBox="0 0 400 100" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Logotipo elementai -->
  <g opacity="1">
    <!-- Notion/Linear stiliaus kvadratas su apvaliais kampais -->
    <rect x="50" y="15" width="70" height="70" rx="14" fill="#4285F4">
      <animate attributeName="opacity" values="0;1" dur="0.8s" begin="0s" fill="freeze" />
    </rect>
    
    <!-- Duomenų analizės elementai - linijinis grafikas -->
    <path d="M65 70L80 40L100 55L115 25" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" opacity="1">
      <animate attributeName="opacity" values="0;1" dur="0.5s" begin="0.3s" fill="freeze" />
      <animate attributeName="stroke-dasharray" from="120 120" to="0 120" dur="1.5s" begin="0.3s" fill="freeze" />
      <animate attributeName="stroke-dashoffset" from="120" to="0" dur="1.5s" begin="0.3s" fill="freeze" />
    </path>
    
    <!-- Duomenų taškai -->
    <circle cx="65" cy="70" r="3.5" fill="white" opacity="1">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="0.6s" fill="freeze" />
    </circle>
    
    <circle cx="80" cy="40" r="3.5" fill="white" opacity="1">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="0.7s" fill="freeze" />
    </circle>
    
    <circle cx="100" cy="55" r="3.5" fill="white" opacity="1">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="0.8s" fill="freeze" />
    </circle>
    
    <circle cx="115" cy="25" r="3.5" fill="white" opacity="1">
      <animate attributeName="opacity" values="0;1" dur="0.3s" begin="0.9s" fill="freeze" />
    </circle>
  </g>
  
  <!-- Tekstas "Roqus Analytics" - Notion/Linear stiliaus šriftas -->
  <g opacity="1">
    <animate attributeName="opacity" values="0;1" dur="1s" begin="1s" fill="freeze" />
    
    <!-- Roqus -->
    <text x="140" y="55" font-family="Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" font-size="36" fill="#4285F4" font-weight="600" letter-spacing="0.2">
      Roqus
    </text>
    
    <!-- Analytics -->
    <text x="240" y="55" font-family="Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" font-size="36" fill="#4285F4" font-weight="400" letter-spacing="0.2">
      Analytics
    </text>
  </g>
</svg>