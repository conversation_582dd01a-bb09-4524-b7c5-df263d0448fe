{"files": [], "references": [{"path": "./config/typescript/tsconfig.app.json"}, {"path": "./config/typescript/tsconfig.node.json"}], "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/shared/*": ["./src/shared/*"], "@/features/*": ["./src/features/*"], "@/app/*": ["./src/app/*"]}, "noImplicitAny": false, "noUnusedParameters": false, "skipLibCheck": true, "allowJs": true, "noUnusedLocals": false, "strictNullChecks": false}}