import React from "react";
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/shared/ui/accordion";

export interface FAQItemData {
  question: string;
  answer: string;
  value: string;
}

interface FAQItemProps {
  item: FAQItemData;
  className?: string;
}

/**
 * Atskiras FAQ klausimas/atsakymas komponentas
 * 
 * @param props - Komponento savybės
 * @param props.item - FAQ klausimo duomenys
 * @param props.className - Papildomi CSS klasių pavadinimai
 */
const FAQItem: React.FC<FAQItemProps> = ({ 
  item, 
  className = "" 
}) => {
  return (
    <AccordionItem
      value={item.value}
      className={`bg-white/90 backdrop-blur-sm border border-nexos-purple/20 rounded-lg overflow-hidden shadow-sm hover:shadow-nexos-purple/20 transition-all duration-300 mb-4 ${className}`}
    >
      <AccordionTrigger className="px-6 py-4 text-nexos-purple hover:text-nexos-purple-light transition-colors">
        {item.question}
      </AccordionTrigger>
      <AccordionContent className="px-6 pb-4 text-[#262B30]">
        {typeof item.answer === 'string' ? (
          <div dangerouslySetInnerHTML={{ __html: item.answer }} />
        ) : (
          item.answer
        )}
      </AccordionContent>
    </AccordionItem>
  );
};

export default FAQItem;
