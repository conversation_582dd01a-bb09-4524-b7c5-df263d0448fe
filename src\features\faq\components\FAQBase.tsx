import React from "react";
import { Accordion } from "@/shared/ui/accordion";
import FAQCategory from "./FAQCategory";
import FAQItem, { FAQItemData } from "./FAQItem";

export interface FAQCategoryData {
  title: string;
  items: FAQItemData[];
}

interface FAQBaseProps {
  /** FAQ klausimų sąrašas (be kategorijų) */
  items?: FAQItemData[];
  /** FAQ klausimų kategorijos */
  categories?: FAQCategoryData[];
  /** Ar rodyti kategorijų pavadinimus */
  showCategories?: boolean;
  /** Papildomi CSS klasių pavadinimai */
  className?: string;
  /** Accordion tipo nustatymas */
  accordionType?: "single" | "multiple";
}

/**
 * Pagrindinis FAQ komponentas, kuris gali rodyti klausimus su kategorijomis arba be jų
 * 
 * @param props - Komponento savybės
 */
const FAQBase: React.FC<FAQBaseProps> = ({
  items = [],
  categories = [],
  showCategories = false,
  className = "",
  accordionType = "single"
}) => {
  // Jei turime kategorijas, naudojame jas
  if (showCategories && categories.length > 0) {
    return (
      <div className={`space-y-8 ${className}`}>
        <Accordion type={accordionType} collapsible className="space-y-4">
          {categories.map((category, categoryIndex) => (
            <FAQCategory
              key={`category-${categoryIndex}`}
              title={category.title}
              items={category.items}
            />
          ))}
        </Accordion>
      </div>
    );
  }

  // Jei turime tik klausimų sąrašą be kategorijų
  if (items.length > 0) {
    return (
      <div className={className}>
        <Accordion type={accordionType} collapsible className="space-y-4">
          {items.map((item, itemIndex) => (
            <FAQItem
              key={item.value || `item-${itemIndex}`}
              item={item}
            />
          ))}
        </Accordion>
      </div>
    );
  }

  // Jei nėra nei klausimų, nei kategorijų
  return (
    <div className={`text-center py-8 ${className}`}>
      <p className="text-gray-500">Klausimų nerasta</p>
    </div>
  );
};

export default FAQBase;
