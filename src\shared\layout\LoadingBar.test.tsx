import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen } from "@testing-library/react";
import { act } from "react";
import LoadingBar from "./LoadingBar";

describe("LoadingBar", () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("renders when isLoading is true", () => {
    render(<LoadingBar isLoading={true} />);
    const loadingBar = screen.getByRole("progressbar", { hidden: true });
    expect(loadingBar).toBeInTheDocument();
  });

  it("starts with progress less than 100%", () => {
    render(<LoadingBar isLoading={true} />);
    const loadingBar = screen.getByRole("progressbar", { hidden: true });
    expect(loadingBar.style.width).not.toBe("100%");
  });

  it("completes to 100% when isLoading becomes false", () => {
    const { rerender } = render(<LoadingBar isLoading={true} />);

    // Pakeičiame į isLoading=false
    rerender(<LoadingBar isLoading={false} />);

    const loadingBar = screen.getByRole("progressbar", { hidden: true });
    expect(loadingBar.style.width).toBe("100%");
  });

  // Šį testą reikia pataisyti, nes LoadingBar komponentas išnyksta iš DOM po animacijos
  it("completes animation after isLoading becomes false", () => {
    const { rerender } = render(<LoadingBar isLoading={true} />);

    // Tikriname, ar LoadingBar rodomas su isLoading=true
    const loadingBarBefore = screen.getByRole("progressbar", { hidden: true });
    expect(loadingBarBefore).toBeInTheDocument();

    // Pakeičiame į isLoading=false
    rerender(<LoadingBar isLoading={false} />);

    // Tikriname, ar LoadingBar turi width: 100% prieš išnykstant
    const loadingBarAfter = screen.getByRole("progressbar", { hidden: true });
    expect(loadingBarAfter.style.width).toBe("100%");

    // Palaukiame, kol baigsis animacija ir komponentas išnyks
    act(() => {
      vi.advanceTimersByTime(300);
    });

    // Šiame taške komponentas jau išnykęs, todėl negalime jo patikrinti
  });
});
