import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { createContext, useContext, ReactNode, useState } from "react";

/**
 * QueryClient kontekstas
 */
const QueryClientContext = createContext<QueryClient | null>(null);

/**
 * Hook'as, kuris leidžia naudoti QueryClient kontekstą
 * 
 * @returns QueryClient instance
 * @throws Error jei naudojamas ne QueryClientProvider viduje
 */
export const useQueryClientContext = () => {
  const context = useContext(QueryClientContext);
  if (!context) {
    throw new Error("useQueryClientContext must be used within a QueryClientProvider");
  }
  return context;
};

interface QueryClientProviderProps {
  children: ReactNode;
  /**
   * Numatytosios QueryClient parinktys
   */
  defaultOptions?: {
    queries?: {
      retry?: boolean | number;
      staleTime?: number;
      cacheTime?: number;
      refetchOnWindowFocus?: boolean;
      refetchOnMount?: boolean;
      refetchOnReconnect?: boolean;
      refetchInterval?: number | false;
      suspense?: boolean;
    };
    mutations?: {
      retry?: boolean | number;
    };
  };
}

/**
 * QueryClient provider komponentas
 * 
 * @param props - Komponento savybės
 * @param props.children - Vaikiniai elementai
 * @param props.defaultOptions - Numatytosios QueryClient parinktys
 */
export const AppQueryClientProvider = ({ 
  children,
  defaultOptions = {
    queries: {
      retry: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      refetchOnReconnect: true,
      refetchInterval: false,
      suspense: false,
    },
    mutations: {
      retry: false,
    },
  }
}: QueryClientProviderProps) => {
  // Sukuriame naują QueryClient instance
  const [queryClient] = useState(() => new QueryClient({ defaultOptions }));
  
  return (
    <QueryClientContext.Provider value={queryClient}>
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </QueryClientContext.Provider>
  );
};

export default AppQueryClientProvider;
