/* Fade in animacija */
.fadeIn {
  animation: fadeIn 0.6s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide in i<PERSON>s */
.slideInLeft {
  animation: slideInLeft 0.6s ease-out forwards;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Slide in i<PERSON> de<PERSON>s */
.slideInRight {
  animation: slideInRight 0.6s ease-out forwards;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Scale in animacija */
.scaleIn {
  animation: scaleIn 0.5s ease-out forwards;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* <PERSON>unce in animacija */
.bounceIn {
  animation: bounceIn 0.8s ease-out forwards;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Pulse animacija */
.pulse {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Glow animacija */
.glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 10px rgba(74, 137, 243, 0.3);
  }
  to {
    box-shadow: 0 0 20px rgba(74, 137, 243, 0.6);
  }
}

/* Shimmer animacija */
.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Rotate animacija */
.rotate {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Float animacija */
.float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Wobble animacija */
.wobble {
  animation: wobble 1s ease-in-out;
}

@keyframes wobble {
  0% { transform: translateX(0%); }
  15% { transform: translateX(-25px) rotate(-5deg); }
  30% { transform: translateX(20px) rotate(3deg); }
  45% { transform: translateX(-15px) rotate(-3deg); }
  60% { transform: translateX(10px) rotate(2deg); }
  75% { transform: translateX(-5px) rotate(-1deg); }
  100% { transform: translateX(0%); }
}

/* Stagger animacija vaikiniams elementams */
.stagger > * {
  animation: fadeIn 0.6s ease-out forwards;
  opacity: 0;
}

.stagger > *:nth-child(1) { animation-delay: 0.1s; }
.stagger > *:nth-child(2) { animation-delay: 0.2s; }
.stagger > *:nth-child(3) { animation-delay: 0.3s; }
.stagger > *:nth-child(4) { animation-delay: 0.4s; }
.stagger > *:nth-child(5) { animation-delay: 0.5s; }
.stagger > *:nth-child(6) { animation-delay: 0.6s; }

/* Hover efektai */
.hoverLift {
  transition: transform 0.3s ease;
}

.hoverLift:hover {
  transform: translateY(-5px);
}

.hoverScale {
  transition: transform 0.3s ease;
}

.hoverScale:hover {
  transform: scale(1.05);
}

.hoverRotate {
  transition: transform 0.3s ease;
}

.hoverRotate:hover {
  transform: rotate(5deg);
}

/* Paused animacija */
.paused {
  animation-play-state: paused;
}

/* Delayed animacija */
.delayed {
  animation-delay: 0.5s;
}

.delayed2 {
  animation-delay: 1s;
}

.delayed3 {
  animation-delay: 1.5s;
}
