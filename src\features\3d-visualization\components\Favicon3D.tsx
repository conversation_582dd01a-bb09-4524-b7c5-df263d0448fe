import React, { Suspense } from "react";
import { Canvas } from "@react-three/fiber";
import {
  OrbitControls,
  PerspectiveCamera,
  Environment,
  ContactShadows,
  Effects,
  Loader,
} from "@react-three/drei";
import { AnimatedChart } from "./AnimatedChart";
import ThreeEffects from "./ThreeEffects";
import { DEFAULT_ORBIT_CONTROLS_CONFIG } from "./types";

// Pagrindinis Favicon3D komponentas
const Favicon3D: React.FC = () => {
  return (
    <div
      style={{
        width: "100%",
        height: "100%",
        background: "radial-gradient(circle, #0a0a20 0%, #050510 100%)",
      }}
    >
      <Canvas shadows dpr={[1, 2]}>
        <Suspense fallback={null}>
          <PerspectiveCamera makeDefault position={[0, 1.5, 3]} fov={40} />

          {/* Apšvietimas */}
          <ambientLight intensity={0.2} />
          <spotLight
            position={[5, 5, 5]}
            angle={0.15}
            penumbra={1}
            intensity={0.8}
            castShadow
            color="#ffffff"
          />
          <pointLight position={[-5, 5, -5]} intensity={0.5} color="#5CEFFF" />
          <pointLight position={[5, -5, 5]} intensity={0.5} color="#B026FF" />

          {/* Pagrindinis 3D grafikas */}
          <AnimatedChart />

          {/* Šešėliai */}
          <ContactShadows
            position={[0, -0.05, 0]}
            opacity={0.5}
            scale={10}
            blur={1.5}
            far={10}
            color="#000000"
          />

          {/* Aplinka */}
          <Environment preset="night" />

          {/* Post-processing efektai */}
          <ThreeEffects />

          {/* Kameros kontrolės */}
          <OrbitControls {...DEFAULT_ORBIT_CONTROLS_CONFIG} />
        </Suspense>
      </Canvas>

      {/* Loader kol kraunasi 3D elementai */}
      <Loader
        containerStyles={{
          background: "radial-gradient(circle, #0a0a20 0%, #050510 100%)",
        }}
        innerStyles={{ background: "#5CEFFF" }}
        barStyles={{ background: "#B026FF" }}
      />
    </div>
  );
};

export default Favicon3D;
