<?xml version="1.0" encoding="UTF-8"?>
<svg width="240" height="60" viewBox="0 0 240 60" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- <PERSON><PERSON>us fonas -->
  <rect width="240" height="60" rx="4" fill="#F5F7FF" opacity="0">
    <animate attributeName="opacity" values="0;1" dur="0.5s" begin="0s" fill="freeze" />
  </rect>
  
  <!-- Logotipo elementai -->
  <g opacity="0">
    <animate attributeName="opacity" values="0;1" dur="0.8s" begin="0.5s" fill="freeze" />
    
    <!-- Notion/Linear stiliaus ikonėlė -->
    <rect x="30" y="15" width="30" height="30" rx="8" fill="#4285F4">
      <animate attributeName="opacity" values="0;1" dur="0.8s" begin="0.8s" fill="freeze" />
    </rect>
    
    <!-- "R" raidė i<PERSON> -->
    <path d="M40 25L40 35M40 25H45C46.5 25 48 26 48 27.5C48 29 46.5 30 45 30H40M45 30L48 35" 
          stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none" opacity="0">
      <animate attributeName="opacity" values="0;1" dur="0.5s" begin="1.2s" fill="freeze" />
      <animate attributeName="stroke-dasharray" from="40 40" to="0 40" dur="1.5s" begin="1.2s" fill="freeze" />
      <animate attributeName="stroke-dashoffset" from="40" to="0" dur="1.5s" begin="1.2s" fill="freeze" />
    </path>
  </g>
  
  <!-- Tekstas "Roqus Analytics" -->
  <g opacity="0">
    <animate attributeName="opacity" values="0;1" dur="1s" begin="1.8s" fill="freeze" />
    
    <!-- Roqus -->
    <text x="75" y="35" font-family="Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" font-size="20" fill="#4285F4" font-weight="600" letter-spacing="0.2">
      Roqus
    </text>
    
    <!-- Analytics -->
    <text x="135" y="35" font-family="Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" font-size="20" fill="#4285F4" font-weight="400" letter-spacing="0.2">
      Analytics
    </text>
  </g>
</svg>