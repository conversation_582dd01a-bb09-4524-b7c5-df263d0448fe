import React from "react";
import {
  EffectComposer,
  Bloom,
  ChromaticAberration,
  Noise,
} from "@react-three/postprocessing";
import {
  PostProcessingConfig,
  DEFAULT_POST_PROCESSING_CONFIG,
  DEFAULT_BLOOM_CONFIG,
  DEFAULT_CHROMATIC_ABERRATION_CONFIG,
  DEFAULT_NOISE_CONFIG
} from "./types";

interface ThreeEffectsProps {
  config?: PostProcessingConfig;
}

/**
 * <PERSON>ras komponentas Three.js post-processing efektams
 * 
 * @param props - Komponento savybės
 * @param props.config - Post-processing efektų konfigūracija
 */
export const ThreeEffects: React.FC<ThreeEffectsProps> = ({ 
  config = DEFAULT_POST_PROCESSING_CONFIG 
}) => {
  // Naudojame numatytąsias konfigūracijas, jei nėra pateiktos
  const bloomConfig = { ...DEFAULT_BLOOM_CONFIG, ...config.bloom };
  const chromaticAberrationConfig = { ...DEFAULT_CHROMATIC_ABERRATION_CONFIG, ...config.chromaticAberration };
  const noiseConfig = { ...DEFAULT_NOISE_CONFIG, ...config.noise };

  return (
    <EffectComposer>
      {/* Bloom efektas švytėjimui */}
      <Bloom
        intensity={bloomConfig.intensity}
        luminanceThreshold={bloomConfig.luminanceThreshold}
        luminanceSmoothing={bloomConfig.luminanceSmoothing}
        height={bloomConfig.height}
        blendFunction={bloomConfig.blendFunction}
      />

      {/* Chromatinė aberacija kraštams */}
      <ChromaticAberration
        offset={chromaticAberrationConfig.offset}
        blendFunction={chromaticAberrationConfig.blendFunction}
        opacity={chromaticAberrationConfig.opacity}
      />

      {/* Subtilus triukšmas holograminiam efektui */}
      <Noise 
        opacity={noiseConfig.opacity} 
        blendFunction={noiseConfig.blendFunction} 
      />
    </EffectComposer>
  );
};

export default ThreeEffects;
