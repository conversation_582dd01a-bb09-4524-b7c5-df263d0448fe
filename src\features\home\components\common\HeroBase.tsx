import { useState, useEffect, ReactNode } from "react";

interface HeroBaseProps {
  /** Hero sekcijos pavadinimas */
  title: string | ReactNode;
  /** <PERSON> sekcijos paantra<PERSON>tė */
  subtitle: string;
  /** Vaikiniai elementai (mygtukai, papildomas turinys) */
  children?: ReactNode;
  /** Fono stiliai */
  backgroundStyle?: React.CSSProperties;
  /** Fono CSS klasės */
  backgroundClassName?: string;
  /** Konteinerio CSS klasės */
  containerClassName?: string;
  /** Turinio CSS klasės */
  contentClassName?: string;
  /** Ar rodyti animaciją */
  showAnimation?: boolean;
  /** Animacijos trukmė milisekundėmis */
  animationDuration?: number;
  /** Animacijos vėlinimas milisekundėmis */
  animationDelay?: number;
}

/**
 * Bendras Hero sekcijos komponentas
 *
 * @param props - Komponento savybės
 */
const HeroBase = ({
  title,
  subtitle,
  children,
  backgroundStyle,
  backgroundClassName = "",
  containerClassName = "nexos-container",
  contentClassName = "",
  showAnimation = true,
  animationDuration = 1000,
  animationDelay = 0
}: HeroBaseProps) => {
  const [isContentVisible, setIsContentVisible] = useState(!showAnimation);

  useEffect(() => {
    if (showAnimation) {
      const timer = setTimeout(() => {
        setIsContentVisible(true);
      }, animationDelay);

      return () => clearTimeout(timer);
    }
  }, [showAnimation, animationDelay]);

  return (
    <section
      className={`py-20 relative overflow-hidden ${backgroundClassName}`}
      style={backgroundStyle}
    >
      <div className={`${containerClassName} relative z-10`}>
        <div className={`max-w-3xl mx-auto text-center ${contentClassName}`}>
          <div
            className={`transition-all transform ${
              isContentVisible
                ? "translate-y-0 opacity-100"
                : "translate-y-8 opacity-0"
            }`}
            style={{
              transitionDuration: `${animationDuration}ms`,
              transitionDelay: showAnimation ? `${animationDelay}ms` : '0ms'
            }}
          >
            <h1 className="nexos-heading mb-4">
              {title}
            </h1>
            <p className="nexos-subheading mb-8">
              {subtitle}
            </p>
            {children && (
              <div className="mt-8">
                {children}
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroBase;
